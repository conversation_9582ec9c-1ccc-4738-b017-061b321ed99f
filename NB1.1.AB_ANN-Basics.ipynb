# CELL_ID: 5
# Import the framework (contains all utilities and verification functions)
PALETTE = "blue-red"  # "red-green" or "blue-red". Color palette to indicate negative to positive values.
from src.notebook_framework import *

# CELL_ID: 15
# TODO: Implement NumpyNeuron class below

class NumpyNeuron:
    def __init__(self, num_inputs):
        self.weights = np.random.randn(num_inputs)
        self.bias = np.random.randn()

    def activation(self, z):
        return 1 / (1 + np.exp(-z))  # Sigmoid

    def forward(self, inputs):
        z = np.dot(inputs, self.weights) + self.bias
        return self.activation(z)
    

verify_numpy_neuron(NumpyNeuron);

# CELL_ID: 18
try:
    # Create a neuron with 3 inputs (like the widget)
    demo_neuron = NumpyNeuron(num_inputs=3)

    print(f"Your Neuron Configuration:\nWeights: {demo_neuron.weights}\nBias: {demo_neuron.bias:.3f}\n")  # type: ignore
    print(f"Testing Different Inputs:\nInputs\t\t\t→ Output\n" + "-" * 35)

    # Test with different input combinations
    test_inputs = [
        [1.0, 0.5, -0.2],   # Mixed values
        [2.0, 2.0, 2.0],    # All positive
        [-1.0, -1.0, -1.0], # All negative
        [0.0, 0.0, 0.0],    # All zeros
    ]

    for inputs in test_inputs:
        output = demo_neuron.forward(np.array(inputs))
        inputs_str = f"[{inputs[0]:4.1f}, {inputs[1]:4.1f}, {inputs[2]:4.1f}]"
        print(f"{inputs_str}\t→ {output:.3f}")

    print(f"\n💡 Notice how the sigmoid activation keeps outputs between 0 and 1.\n   Next: Try the interactive version below ↓")

    # Mark neuron demo as complete
    update_progress('neuron_demo')

except Exception as e:
    print(f"❌ Exception: {e}")

# CELL_ID: 20
# Interactive Neuron Visualization
from src.numpy_neuron_widget import create_interactive_neuron_demo
create_interactive_neuron_demo(PALETTE)

# CELL_ID: 26
# TODO: Implement NumpyLayer class below

class NumpyLayer:
    def __init__(self, num_inputs, num_neurons):
        # YOUR CODE HERE!
        # Hint: self.weights should have shape (num_inputs, num_neurons) - each column is one neuron
        # Hint: self.biases should have shape (num_neurons,)
        self.weights = np.random.randn(num_inputs, num_neurons)
        self.biases = np.random.randn(num_neurons)

    def activation(self, z):
        # YOUR CODE HERE!
        # Hint: both z and the output should have shape (num_neurons,)
        return 1 / (1 + np.exp(-z))

    def forward(self, inputs):
        # YOUR CODE HERE!
        # Hint: Multiply inputs with weights, add bias, then apply activation element-wise
        # Hint: You can use the operator @ instead of np.dot() if you prefer
        z = np.dot(inputs, self.weights) + self.biases
        return self.activation(z)

# Verify your implementation
verify_numpy_layer(NumpyLayer);  # Example instantiation to check for errors

# CELL_ID: 29
# Create a layer with 3 inputs and 4 neurons (like a small hidden layer)

try:
    demo_layer = NumpyLayer(num_inputs=3, num_neurons=4)

    print(f"Your Layer Configuration:")
    print(f"Weights shape: {demo_layer.weights.shape} (each column = one neuron's weights)")  # type: ignore
    print(f"Biases shape: {demo_layer.biases.shape} (one bias per neuron)")  # type: ignore
    print(f"Weights:\n{demo_layer.weights}")  # type: ignore
    print(f"Biases: {demo_layer.biases}\n")  # type: ignore
    print(f"Testing Different Inputs:\nInputs\t\t\t→ Outputs (4 neurons)\n" + "-" * 50)

    # Test with different input combinations
    test_inputs = [
        [1.0, 0.5, -0.2],   # Mixed values
        [2.0, 2.0, 2.0],    # All positive
        [-1.0, -1.0, -1.0], # All negative
        [0.0, 0.0, 0.0],    # All zeros
    ]
    for inputs in test_inputs:
        outputs = demo_layer.forward(np.array(inputs))
        inputs_str = f"[{inputs[0]:4.1f}, {inputs[1]:4.1f}, {inputs[2]:4.1f}]"  # type: ignore
        outputs_str = f"[{outputs[0]:.3f}, {outputs[1]:.3f}, {outputs[2]:.3f}, {outputs[3]:.3f}]"  # type: ignore
        print(f"{inputs_str}\t→ {outputs_str}")
    print(f"\n💡 Notice how each neuron produces different outputs from the same inputs.")
    print(f"   Each column in the weight matrix creates a different 'feature detector'!")

    # Mark layer demo as complete! 🎉
    update_progress('layer_demo')

except Exception as e:
    print(f"❌ Exception: {e}")

# Check if notebook is completed
check_notebook_complete()

