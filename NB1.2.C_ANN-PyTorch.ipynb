# CELL_ID: 5
# Import the framework (contains all utilities and verification functions)
PALETTE = "blue-red"  # "red-green" or "blue-red". Color palette to indicate negative to positive values.
from src.notebook_framework import *

from src.notebook_framework import setup_device
import torch

# Check if CUDA is available
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"CUDA version: {torch.version.cuda}")
print(f"Number of GPUs: {torch.cuda.device_count()}")

# Get GPU device names
if torch.cuda.is_available():
    for i in range(torch.cuda.device_count()):
        print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
        device = torch.device(f"cuda:{i}"
device  # Set the device to the first GPU

# CELL_ID: 6
# Progress Management - Delete Progress Button
import ipywidgets as widgets
from IPython.display import display, clear_output
import json
from pathlib import Path

def delete_progress_file():
    """Delete the progress file and reset all progress"""
    progress_file = Path("course_progress.json")
    if progress_file.exists():
        progress_file.unlink()
        return True
    return False

def create_delete_progress_button():
    """Create an interactive button to delete progress file"""
    delete_button = widgets.Button(
        description="🗑️ Delete Progress",
        button_style='danger',
        tooltip='Delete all progress data and reset the course'
    )

    output = widgets.Output()

    def on_delete_click(b):
        with output:
            clear_output()
            # Show confirmation
            confirm_text = widgets.HTML(
                value="<p style='color: orange; font-weight: bold;'>⚠️ This will delete ALL progress data. Are you sure?</p>"
            )

            confirm_button = widgets.Button(
                description="Yes, Delete All",
                button_style='danger'
            )

            cancel_button = widgets.Button(
                description="Cancel",
                button_style='success'
            )

            def on_confirm(b):
                with output:
                    clear_output()
                    success = delete_progress_file()
                    if success:
                        display(widgets.HTML(
                            value="<p style='color: green; font-weight: bold;'>✅ Progress file deleted successfully!</p>"
                        ))
                    else:
                        display(widgets.HTML(
                            value="<p style='color: red; font-weight: bold;'>❌ No progress file found to delete.</p>"
                        ))

            def on_cancel(b):
                with output:
                    clear_output()
                    display(widgets.HTML(
                        value="<p style='color: blue;'>Operation cancelled.</p>"
                    ))

            confirm_button.on_click(on_confirm)
            cancel_button.on_click(on_cancel)

            display(confirm_text)
            display(widgets.HBox([confirm_button, cancel_button]))

    delete_button.on_click(on_delete_click)

    return widgets.VBox([
        widgets.HTML(value="<h4>🎯 Progress Management</h4>"),
        delete_button,
        output
    ])

# Display the delete progress button
display(create_delete_progress_button())

# CELL_ID: 10
# Interactive quiz to test understanding of automatic differentiation concepts
create_autograd_quiz()

# CELL_ID: 13

# TODO: Implement TorchPerceptron class below

class TorchPerceptron(nn.Module):
    def __init__(self, layer_sizes):
        super().__init__()
        # YOUR CODE HERE!
        # Hint: Build layers using nn.Sequential
        # Hint: Iterate through layer_sizes pairs: zip(layer_sizes[:-1], layer_sizes[1:])
        # Hint: Add nn.Linear(in_size, out_size) and nn.ReLU() (except after the last layer)
        pass

    def forward(self, x):
        # YOUR CODE HERE!
        # Hint: Just pass x through self.layers
        pass

class TorchPerceptron(nn.Module):
    def __init__(self, layer_sizes):
        super().__init__()

        # Build sequential network from layer_sizes
        layers = []
        for i in range(len(layer_sizes) - 1):
            layers.append(nn.Linear(layer_sizes[i], layer_sizes[i+1]))
            # Add ReLU after all layers except the last (for CrossEntropyLoss)
            if i < len(layer_sizes) - 2:
                layers.append(nn.ReLU())

        self.layers = nn.Sequential(*layers)

    def forward(self, x):
        return self.layers(x)


# CELL_ID: 15
verify_torch_perceptron(TorchPerceptron)  # Test your implementation

# CELL_ID: 16
# Example model with 2 hidden layers and 1 output layer.
model_example = TorchPerceptron([3, 8, 8, 2])  # 3 inputs → 8 → 8 → 2 outputs
x = torch.randn(5, 3)  # Batch of 5 samples, 3 input features each (will output 2 values per input sample)
output = model_example(x)
print(f"Input shape: {x.shape}\nOutput shape: {output.shape}")
print(f"\n{output}")

# CELL_ID: 19
# Visualize your PyTorch model with VU meters
model_to_train = TorchPerceptron([4, 6, 2])  # (4 input values) → 6 hidden neurons → 2 output neurons (→ 2 output values)
visualizer = NeuralNetworkVisualizer(model_to_train, PALETTE)
visualizer.create_widget()

# CELL_ID: 24
# Create the training visualizer with selectable activation function. (A new model is created internally)
training_viz = TrainingNetworkVisualizer(
    layer_sizes=[1, 8, 8, 2],   # Network architecture, e.g. layers: [1, 8, 8, 2]
    train_size=60,              # Compare e.g. 6 and 60
    val_size=100,               # 100 evenly spaced points for validation (no need to change this one)
    batch_size=8,               # Batch size (number of training samples to batch for each model update)
    learning_rate=0.006,        # Learning rate
    palette=PALETTE,
    sine_frequency=6,           # Controls the frequency of the desired sine wave
)
training_viz.create_widget()