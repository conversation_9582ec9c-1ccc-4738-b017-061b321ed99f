# CELL_ID: 5
# Import the framework and standard libraries
import time
import json
from datetime import datetime
from pathlib import Path

# Import our course framework
from src.notebook_framework import *

# Import helper functions for NB1.3
from src.nb13_helper_functions import *

# Additional imports for this notebook
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report

# Set style for professional plots
plt.style.use('seaborn-v0_8-darkgrid')
sns.set_palette("husl")

# CELL_ID: 10
# TODO: Implement your data loading pipeline here
# Hint: Start by defining the transforms
# Hint: Use torch.utils.data.random_split for splitting
# Hint: Remember to unpack settings with **get_data_loader_settings(device)

# YOUR CODE HERE


# CELL_ID: 11
# Verify your implementation
try:
    verify_data_loading(train_loader, val_loader, test_loader)
    print("\n✅ Excellent! Your data pipeline is production-ready.")
except NameError:
    print("❌ Please implement the data loaders first (train_loader, val_loader, test_loader)")
except Exception as e:
    print(f"❌ Error in data loading: {e}")

# CELL_ID: 13
# TODO: Implement your data exploration visualizations
# Hint: Use iter() and next() to get a batch from the DataLoader
# Hint: Remember to move tensors to CPU for plotting: .cpu()
# Hint: Use plt.subplots() for creating grids

# YOUR CODE HERE


# CELL_ID: 16
# Define our model architecture (same as NB1.2)
class TorchPerceptron(nn.Module):
    def __init__(self, layer_sizes, dropout_rate=0.2):
        super().__init__()

        layers = []
        for i in range(len(layer_sizes) - 1):
            # Add linear layer
            layers.append(nn.Linear(layer_sizes[i], layer_sizes[i+1]))

            # Add ReLU and Dropout for all except last layer
            if i < len(layer_sizes) - 2:
                layers.append(nn.ReLU())
                if dropout_rate > 0:
                    layers.append(nn.Dropout(dropout_rate))

        self.layers = nn.Sequential(*layers)

    def forward(self, x):
        # Flatten the input images
        x = x.view(x.size(0), -1)
        return self.layers(x)

# Create model instance
model = TorchPerceptron([784, 256, 128, 10], dropout_rate=0.2).to(device)
print(f"Model created with {sum(p.numel() for p in model.parameters()):,} parameters")
print(f"Running on: {device}")

# CELL_ID: 18
# Professional configuration setup
config = {
    "model_name": "mnist_mlp",
    "learning_rate": 0.001,
    "num_epochs": 20,
    "batch_size": 128,
    "architecture": [784, 256, 128, 10],
    "dropout_rate": 0.2,
    "optimizer": "Adam",
    "weight_decay": 1e-4,
    "device": str(device),
    "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S")
}

# Create output directory for this run
output_dir = Path(f"runs/{config['model_name']}_{config['timestamp']}")
output_dir.mkdir(parents=True, exist_ok=True)

# Save configuration
with open(output_dir / "config.json", "w") as f:
    json.dump(config, f, indent=2)

print(f"Configuration saved to: {output_dir}")
print(json.dumps(config, indent=2))

# CELL_ID: 20
# TODO: Implement your professional training loop
# Structure suggestion:
# 1. Define calculate_accuracy function
# 2. Define train_epoch function
# 3. Define validate_epoch function
# 4. Define main training loop with early stopping

# YOUR CODE HERE


# CELL_ID: 22
# TODO: Create your training visualization
# Hint: Use plt.subplots(1, 2, figsize=(12, 5)) for side-by-side plots
# Hint: Use ax.axvline() to mark the best epoch
# Hint: Save with plt.savefig(output_dir / "training_curves.png", dpi=300, bbox_inches='tight')

# YOUR CODE HERE


# CELL_ID: 25
# TODO: Implement comprehensive model evaluation
# Steps:
# 1. Load best checkpoint
# 2. Run evaluation on test set
# 3. Generate confusion matrix
# 4. Create classification report
# 5. Visualize results

# YOUR CODE HERE


# CELL_ID: 27
# TODO: Implement failure analysis
# Hint: Collect misclassified examples during evaluation
# Hint: Use torch.softmax to get confidence scores
# Hint: Sort by confidence to show the most confident mistakes

# YOUR CODE HERE


# CELL_ID: 30
# TODO: Implement performance benchmarking
# Note: Be careful not to train for too long - just enough to measure
# Hint: Use time.time() for accurate measurements
# Hint: Run multiple times and average for more accurate results

# YOUR CODE HERE


# CELL_ID: 33
# TODO: Implement experiment tracking
# Create a comprehensive summary of your experiment
# Save it in a format that's both machine and human readable

# YOUR CODE HERE


# CELL_ID: 35
# TODO: Implement reproducibility checklist
# This should generate a report confirming all reproducibility requirements are met

# YOUR CODE HERE
