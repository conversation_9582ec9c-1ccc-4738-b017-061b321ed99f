"use strict";(self.webpackChunkjupyterlab_jupytext=self.webpackChunkjupyterlab_jupytext||[]).push([[720],{720:(e,t,n)=>{n.r(t),n.d(t,{IRisePreviewFactory:()=>R,IRisePreviewTracker:()=>b,default:()=>x});var o=n(549),i=n(560),s=n(88),a=n(542),r=n(918),l=n(53),d=n(484);const c=new d.LabIcon({name:"RISE",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" height="16" viewBox="0 0 24 24" width="16" fill="none" stroke="#4f4f4f">\n    <path d="M15 14.25l3 6m-9-6l-3 6m-4-16h20-2v10H4v-10z" class="jp-icon3" stroke-width="2" />\n    <path d="M9.6 11.6v-1.5m-1.5 1.5V6.1m-1.5 5.5V8.1m5.5 3.5h3m-3-1.5h6m-6-1.5h6m-6-2h5" class="jp-icon3"\n        stroke-width="1.2" />\n</svg>'}),u=new d.LabIcon({name:"RISE:fullScreen",svgstr:'<svg xmlns="http://www.w3.org/2000/svg" height="16" viewBox="0 0 24 24" width="16" fill="#4f4f4f">\n    <path class="jp-icon3" d="M22 22v-6h-2v4h-4v2zM2 22h6v-2H4v-4H2zM22 2h-6v2h4v4h2zM2 2v6h2V4h4V2z" />\n</svg>'});var h,v=n(689),p=n(375),g=n(262),m=n(12),w=n(602),y=n(256);class f extends p.DocumentWidget{constructor(e){super({...e,content:new i.IFrame({sandbox:["allow-same-origin","allow-scripts","allow-downloads","allow-modals","allow-popups"]})}),this._ready=new g.PromiseDelegate;const{getRiseUrl:t,context:n,renderOnSave:o,translator:s}=e;this.getRiseUrl=t,this._path=n.path;const a=(null!=s?s:r.nullTranslator).load("rise");this.content.title.icon=c,this._renderOnSave=null!=o&&o,n.pathChanged.connect((()=>{this.path=n.path}));const l=new i.ToolbarButton({icon:d.refreshIcon,tooltip:a.__("Reload Preview"),onClick:()=>{this.reload()}}),v=new h.CheckBox({checked:this._renderOnSave,onChange:e=>{var t,n;this._renderOnSave=null!==(n=null===(t=e.target)||void 0===t?void 0:t.checked)&&void 0!==n&&n},translator:s});this.toolbar.addItem("fullscreen",new i.ToolbarButton({icon:u,tooltip:a.__("Open the slideshow in full screen"),onClick:()=>{e.commands.execute("RISE:fullscreen-plugin")}})),n&&(this.toolbar.addItem("renderOnSave",v),n.ready.then((()=>{this.setActiveCellIndex(0).then((()=>this._ready.resolve())).catch((e=>this._ready.reject(e))),n.fileChanged.connect((()=>{this.renderOnSave&&this.reload()}))}))),this.toolbar.addItem("spacer",i.Toolbar.createSpacerItem()),this.toolbar.addItem("reload",l)}get ready(){return this._ready.promise}get iframe(){return this.content.node.querySelector("iframe")}dispose(){this.isDisposed||(super.dispose(),w.Signal.clearData(this))}reload(){const e=this.content.node.querySelector("iframe");e.contentWindow&&e.contentWindow.location.reload()}get renderOnSave(){return this._renderOnSave}async setActiveCellIndex(e,t=!0){const n=this.iframe;if(!t)return n.contentWindow?(n.contentWindow.history.pushState(null,"",this.getRiseUrl(this.path,e)),Promise.resolve()):Promise.reject("No content.");this.content.url=this.getRiseUrl(this.path,e),await this._waitForIFrame(n)}get path(){return this._path}set path(e){e!==this._path&&(this._path=e,this.setActiveCellIndex(0))}async _waitForIFrame(e){var t,n;const o=new g.PromiseDelegate,i=()=>{var t;null===(t=e.contentWindow)||void 0===t||t.removeEventListener("load",i);const n=setInterval((()=>{var t;(null===(t=e.contentDocument)||void 0===t?void 0:t.querySelector(".reveal"))&&(clearInterval(n),o.resolve(!0))}),500)};return"complete"===(null===(t=e.contentDocument)||void 0===t?void 0:t.readyState)?i():null===(n=e.contentWindow)||void 0===n||n.addEventListener("load",i),o.promise}}!function(e){function t(e,t){let n=`${v.PageConfig.getBaseUrl()}rise/${e}`;return"number"==typeof t&&(n+=v.URLExt.objectToQueryString({activeCellIndex:t})),n}e.FACTORY_NAME="rise",e.getRiseUrl=t,e.FactoryToken=class{constructor({commands:e,docRegistry:t,fileTypes:n,translator:o}){this._disposeFactory=null,this._widgetCreated=new w.Signal(this),this._commands=e,this._docRegistry=t,this._fileTypes=null!=n?n:["notebook"],this._translator=null!=o?o:r.nullTranslator,this._updateFactory()}addFileType(e){this._fileTypes.includes(e)||(this._fileTypes.push(e),this._updateFactory())}get widgetCreated(){return this._widgetCreated}_updateFactory(){this._disposeFactory&&(this._disposeFactory.dispose(),this._disposeFactory=null);const n=this._translator.load("rise"),o=new _(t,this._commands,{name:e.FACTORY_NAME,label:n.__("Rise Slides"),fileTypes:this._fileTypes,modelName:"notebook"});o.widgetCreated.connect(((e,t)=>{this._widgetCreated.emit(t)}),this),this._disposeFactory=m.DisposableSet.from([this._docRegistry.addWidgetFactory(o),o])}}}(f||(f={}));class _ extends p.ABCWidgetFactory{constructor(e,t,n){super(n),this.getRiseUrl=e,this.commands=t,this.defaultRenderOnSave=!1}createNewWidget(e){return new f({context:e,commands:this.commands,getRiseUrl:this.getRiseUrl,renderOnSave:this.defaultRenderOnSave})}}!function(e){class t extends y.Widget{constructor(e={}){var t,n,o;const i=(null!==(t=e.translator)&&void 0!==t?t:r.nullTranslator).load("rise"),s=document.createElement("label");s.insertAdjacentHTML("afterbegin",`<input name="renderOnSave" type="checkbox"></input>${i.__("Render on Save")}`),super({node:s}),this.input=s.childNodes.item(0),this.checked=null!==(n=e.checked)&&void 0!==n&&n,this.onChange=null!==(o=e.onChange)&&void 0!==o?o:()=>{}}get checked(){return this.input.checked}set checked(e){this.input.checked=e}onAfterAttach(e){super.onAfterAttach(e),this.input.addEventListener("change",this.onChange)}onBeforeDetach(e){this.input.removeEventListener("change",this.onChange),super.onBeforeDetach(e)}}e.CheckBox=t,e.setupLog=function(){window.onmessage=e=>{var t,n,o,i,s,a,r,l,d;switch(null===(t=e.data)||void 0===t?void 0:t.level){case"debug":console.debug(...null!==(o=null===(n=e.data)||void 0===n?void 0:n.msg)&&void 0!==o?o:[]);break;case"info":console.info(...null!==(s=null===(i=e.data)||void 0===i?void 0:i.msg)&&void 0!==s?s:[]);break;case"warn":console.warn(...null!==(r=null===(a=e.data)||void 0===a?void 0:a.msg)&&void 0!==r?r:[]);break;case"error":console.error(...null!==(d=null===(l=e.data)||void 0===l?void 0:l.msg)&&void 0!==d?d:[]);break;default:console.log(e)}}}}(h||(h={}));const b=new g.Token("jupyterlab-rise:IRisePreviewTracker","Adds a tracker for RISE slides preview widgets."),R=new g.Token("jupyterlab-rise:IRisePreviewFactory","Customize the RISE slides preview factory.");var S;!function(e){e.openRise="RISE:slideshow",e.riseFullScreen="RISE:fullscreen-plugin",e.risePreview="RISE:preview",e.riseSetSlideType="RISE:set-slide-type"}(S||(S={}));const C={id:"jupyterlab-rise:factory",provides:R,optional:[r.ITranslator],activate:(e,t)=>{const{commands:n,docRegistry:o}=e;return new f.FactoryToken({commands:n,docRegistry:o,translator:null!=t?t:void 0})}},I={id:"jupyterlab-rise:plugin",autoStart:!0,requires:[R],optional:[s.INotebookTracker,i.ICommandPalette,o.ILayoutRestorer,a.ISettingRegistry,r.ITranslator],provides:b,activate:(e,t,n,o,s,a,d)=>{console.log("JupyterLab extension jupyterlab-rise is activated!");const h=new i.WidgetTracker({namespace:"rise"});if(!n)return h;const{commands:v,shell:p}=e,g=(null!=d?d:r.nullTranslator).load("rise");let m=null;function w(){return null!==(null==n?void 0:n.currentWidget)&&(null==n?void 0:n.currentWidget)===p.currentWidget}if(a&&a.load(I.id).then((e=>{m=e})),s&&s.restore(h,{command:"docmanager:open",args:e=>({path:e.context.path,factory:f.FACTORY_NAME}),name:e=>e.context.path,when:e.serviceManager.ready}),t.widgetCreated.connect(((e,t)=>{t.context.pathChanged.connect((()=>{h.save(t)})),h.add(t)})),h.widgetAdded.connect(((e,t)=>{t.ready.then((()=>{var e;return null===(e=t.iframe)||void 0===e?void 0:e.focus()}))})),v.addCommand(S.openRise,{label:e=>e.toolbar?"":g.__("Open as Reveal Slideshow"),caption:g.__("Open the current notebook in a new browser tab as an RevealJS slideshow."),execute:async()=>{const e=n.currentWidget;e&&(await e.context.save(),window.open(f.getRiseUrl(e.context.path,e.content.activeCellIndex)))},isEnabled:w}),v.addCommand(S.risePreview,{label:e=>e.toolbar?"":g.__("Render as Reveal Slideshow"),caption:g.__("Render the current notebook as Reveal Slideshow"),icon:c,execute:async e=>{const t=function(e){var t;const o=null!==(t=null==n?void 0:n.currentWidget)&&void 0!==t?t:null;return!1!==e.activate&&o&&p.activateById(o.id),o}(e);let o;if(t){o=t.context,await o.save();const n=await v.execute("docmanager:open",{path:o.path,factory:f.FACTORY_NAME,options:{mode:"split-right"}}),s=e=>{n.setActiveCellIndex(e.activeCellIndex,!1)};n.setActiveCellIndex(t.content.activeCellIndex),t.content.activeCellChanged.connect(s),n.disposed.connect((()=>{t.content.activeCellChanged.disconnect(s)})),!0===e.fullscreen&&n.ready.then((()=>{(0,i.showDialog)({title:g.__("Switch to full screen"),body:g.__("The slideshow is set to automatically open in full screen. Your web browser requires your confirmation to do so.")}).then((e=>{e.button.accept&&v.execute(S.riseFullScreen,{id:n.id})}))})).catch((e=>{console.log(e)}))}},isEnabled:w}),v.addCommand(S.riseFullScreen,{label:g.__("Full screen slideshow"),caption:g.__("Toggle full screen the current active slideshow"),icon:u,execute:async t=>{var n;t.id&&e.shell.activateById(t.id);const o=t.id?(0,l.toArray)(e.shell.widgets("main")).find((e=>e.id===t.id)):e.shell.currentWidget;if(o&&h.has(o)){const e=o.iframe;e&&(document.fullscreenElement||(null===(n=e.contentDocument)||void 0===n?void 0:n.fullscreenElement)?document.exitFullscreen&&await document.exitFullscreen():o.ready.then((()=>{var t,n;null===(n=null===(t=null==e?void 0:e.contentWindow)||void 0===t?void 0:t.document.querySelector("div.reveal"))||void 0===n||n.requestFullscreen()})),e.focus())}},isEnabled:()=>!!e.shell.currentWidget&&h.has(e.shell.currentWidget)}),v.addCommand(S.riseSetSlideType,{label:e=>g.__("Toggle slideshow %1 type",e.type),caption:e=>g.__("(Un)set active cell as a %1 cell",e.type),execute:t=>{var o,i;const s=t.type,a=e.shell.currentWidget;if(a&&n.has(a)){const e=null===(o=a.content.activeCell)||void 0===o?void 0:o.model;if(e){const t=null!==(i=e.getMetadata("slideshow"))&&void 0!==i?i:{};if(s!==t.slide_type){const n={...t};s?n.slide_type=s:delete n.slide_type,Object.keys(n).length>0?e.setMetadata("slideshow",n):e.deleteMetadata("slideshow")}}}},isToggled:t=>{var o,i;const s=t.type,a=e.shell.currentWidget;if(a&&n.has(a)){const e=null===(o=a.content.activeCell)||void 0===o?void 0:o.model;if(e)return(null!==(i=e.getMetadata("slideshow"))&&void 0!==i?i:{}).slide_type===s&&!!s}return!1},isEnabled:e=>{var t;return["slide","subslide","fragment","skip","notes"].includes(null!==(t=e.type)&&void 0!==t?t:"")}}),n.widgetAdded.connect((async(t,n)=>{var o,s,a;if(n.toolbar.insertBefore("kernelName","RISE-button",new i.CommandToolbarButton({commands:v,id:S.risePreview,args:{toolbar:!0}})),"Rise"!==e.name){await n.context.ready;let e=null!==(a=(null!==(s=null===(o=n.content.model)||void 0===o?void 0:o.getMetadata("rise"))&&void 0!==s?s:{}).autolaunch)&&void 0!==a&&a;m&&(e|=m.get("autolaunch").composite),e&&await v.execute(S.risePreview,{fullscreen:!0})}})),o){const e="Notebook Operations";[S.openRise,S.risePreview].forEach((t=>{o.addItem({command:t,category:e})}))}return h}},x=[C,I]}}]);