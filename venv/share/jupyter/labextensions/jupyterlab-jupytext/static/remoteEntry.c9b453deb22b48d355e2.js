var _JUPYTERLAB;(()=>{"use strict";var e,r,t,a,n,o,u,i,l,f,s,p,d,c,h,b,v,y,m,g,j,w,S,P,x,k={139:(e,r,t)=>{var a={"./index":()=>Promise.all([t.e(25),t.e(509)]).then((()=>()=>t(509))),"./extension":()=>Promise.all([t.e(25),t.e(509)]).then((()=>()=>t(509)))},n=(e,r)=>(t.R=r,r=t.o(a,e)?a[e]():Promise.resolve().then((()=>{throw new Error('Module "'+e+'" does not exist in container.')})),t.R=void 0,r),o=(e,r)=>{if(t.S){var a="default",n=t.S[a];if(n&&n!==e)throw new Error("Container initialization failed as it has already been initialized with a different share scope");return t.S[a]=e,t.I(a,r)}};t.d(r,{get:()=>n,init:()=>o})}},E={};function T(e){var r=E[e];if(void 0!==r)return r.exports;var t=E[e]={exports:{}};return k[e].call(t.exports,t,t.exports,T),t.exports}T.m=k,T.c=E,T.d=(e,r)=>{for(var t in r)T.o(r,t)&&!T.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},T.f={},T.e=e=>Promise.all(Object.keys(T.f).reduce(((r,t)=>(T.f[t](e,r),r)),[])),T.u=e=>e+"."+{48:"4172aded109d990daa22",509:"5a03966e9e40908cdb52",720:"9eca4804836bdb508b4d"}[e]+".js?v="+{48:"4172aded109d990daa22",509:"5a03966e9e40908cdb52",720:"9eca4804836bdb508b4d"}[e],T.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),T.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),e={},r="jupyterlab-jupytext:",T.l=(t,a,n,o)=>{if(e[t])e[t].push(a);else{var u,i;if(void 0!==n)for(var l=document.getElementsByTagName("script"),f=0;f<l.length;f++){var s=l[f];if(s.getAttribute("src")==t||s.getAttribute("data-webpack")==r+n){u=s;break}}u||(i=!0,(u=document.createElement("script")).charset="utf-8",u.timeout=120,T.nc&&u.setAttribute("nonce",T.nc),u.setAttribute("data-webpack",r+n),u.src=t),e[t]=[a];var p=(r,a)=>{u.onerror=u.onload=null,clearTimeout(d);var n=e[t];if(delete e[t],u.parentNode&&u.parentNode.removeChild(u),n&&n.forEach((e=>e(a))),r)return r(a)},d=setTimeout(p.bind(null,void 0,{type:"timeout",target:u}),12e4);u.onerror=p.bind(null,u.onerror),u.onload=p.bind(null,u.onload),i&&document.head.appendChild(u)}},T.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{T.S={};var e={},r={};T.I=(t,a)=>{a||(a=[]);var n=r[t];if(n||(n=r[t]={}),!(a.indexOf(n)>=0)){if(a.push(n),e[t])return e[t];T.o(T.S,t)||(T.S[t]={});var o=T.S[t],u="jupyterlab-jupytext",i=(e,r,t,a)=>{var n=o[e]=o[e]||{},i=n[r];(!i||!i.loaded&&(!a!=!i.eager?a:u>i.from))&&(n[r]={get:t,from:u,eager:!!a})},l=[];return"default"===t&&(i("buffer","6.0.3",(()=>T.e(48).then((()=>()=>T(48))))),i("jupyterlab-jupytext","1.4.4",(()=>Promise.all([T.e(25),T.e(509)]).then((()=>()=>T(509))))),i("jupyterlab-rise","0.43.1",(()=>Promise.all([T.e(720),T.e(25),T.e(88)]).then((()=>()=>T(720)))))),e[t]=l.length?Promise.all(l).then((()=>e[t]=1)):1}}})(),(()=>{var e;T.g.importScripts&&(e=T.g.location+"");var r=T.g.document;if(!e&&r&&(r.currentScript&&"SCRIPT"===r.currentScript.tagName.toUpperCase()&&(e=r.currentScript.src),!e)){var t=r.getElementsByTagName("script");if(t.length)for(var a=t.length-1;a>-1&&(!e||!/^http(s?):/.test(e));)e=t[a--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),T.p=e})(),t=e=>{var r=e=>e.split(".").map((e=>+e==e?+e:e)),t=/^([^-+]+)?(?:-([^+]+))?(?:\+(.+))?$/.exec(e),a=t[1]?r(t[1]):[];return t[2]&&(a.length++,a.push.apply(a,r(t[2]))),t[3]&&(a.push([]),a.push.apply(a,r(t[3]))),a},a=(e,r)=>{e=t(e),r=t(r);for(var a=0;;){if(a>=e.length)return a<r.length&&"u"!=(typeof r[a])[0];var n=e[a],o=(typeof n)[0];if(a>=r.length)return"u"==o;var u=r[a],i=(typeof u)[0];if(o!=i)return"o"==o&&"n"==i||"s"==i||"u"==o;if("o"!=o&&"u"!=o&&n!=u)return n<u;a++}},n=e=>{var r=e[0],t="";if(1===e.length)return"*";if(r+.5){t+=0==r?">=":-1==r?"<":1==r?"^":2==r?"~":r>0?"=":"!=";for(var a=1,o=1;o<e.length;o++)a--,t+="u"==(typeof(i=e[o]))[0]?"-":(a>0?".":"")+(a=2,i);return t}var u=[];for(o=1;o<e.length;o++){var i=e[o];u.push(0===i?"not("+l()+")":1===i?"("+l()+" || "+l()+")":2===i?u.pop()+" "+u.pop():n(i))}return l();function l(){return u.pop().replace(/^\((.+)\)$/,"$1")}},o=(e,r)=>{if(0 in e){r=t(r);var a=e[0],n=a<0;n&&(a=-a-1);for(var u=0,i=1,l=!0;;i++,u++){var f,s,p=i<e.length?(typeof e[i])[0]:"";if(u>=r.length||"o"==(s=(typeof(f=r[u]))[0]))return!l||("u"==p?i>a&&!n:""==p!=n);if("u"==s){if(!l||"u"!=p)return!1}else if(l)if(p==s)if(i<=a){if(f!=e[i])return!1}else{if(n?f>e[i]:f<e[i])return!1;f!=e[i]&&(l=!1)}else if("s"!=p&&"n"!=p){if(n||i<=a)return!1;l=!1,i--}else{if(i<=a||s<p!=n)return!1;l=!1}else"s"!=p&&"n"!=p&&(l=!1,i--)}}var d=[],c=d.pop.bind(d);for(u=1;u<e.length;u++){var h=e[u];d.push(1==h?c()|c():2==h?c()&c():h?o(h,r):!c())}return!!c()},u=(e,r)=>e&&T.o(e,r),i=e=>(e.loaded=1,e.get()),l=e=>Object.keys(e).reduce(((r,t)=>(e[t].eager&&(r[t]=e[t]),r)),{}),f=(e,r,t)=>{var n=t?l(e[r]):e[r];return(r=Object.keys(n).reduce(((e,r)=>!e||a(e,r)?r:e),0))&&n[r]},s=(e,r,t,n)=>{var u=n?l(e[r]):e[r];return(r=Object.keys(u).reduce(((e,r)=>!o(t,r)||e&&!a(e,r)?e:r),0))&&u[r]},p=(e,r,t)=>{var n=t?l(e[r]):e[r];return Object.keys(n).reduce(((e,r)=>!e||!n[e].loaded&&a(e,r)?r:e),0)},d=(e,r,t,a)=>"Unsatisfied version "+t+" from "+(t&&e[r][t].from)+" of shared singleton module "+r+" (required "+n(a)+")",c=(e,r,t,a,o)=>{var u=e[t];return"No satisfying version ("+n(a)+")"+(o?" for eager consumption":"")+" of shared module "+t+" found in shared scope "+r+".\nAvailable versions: "+Object.keys(u).map((e=>e+" from "+u[e].from)).join(", ")},h=e=>{throw new Error(e)},b=e=>{"undefined"!=typeof console&&console.warn&&console.warn(e)},y=(e,r,t)=>t?t():((e,r)=>h("Shared module "+r+" doesn't exist in shared scope "+e))(e,r),m=(v=e=>function(r,t,a,n,o){var u=T.I(r);return u&&u.then&&!a?u.then(e.bind(e,r,T.S[r],t,!1,n,o)):e(r,T.S[r],t,a,n,o)})(((e,r,t,a,n,o)=>{if(!u(r,t))return y(e,t,o);var l=s(r,t,n,a);return l?i(l):(b(c(r,e,t,n,a)),i(f(r,t,a)))})),g=v(((e,r,t,a,n,o)=>{if(!u(r,t))return y(e,t,o);var l=s(r,t,n,a);return l?i(l):o?o():void h(c(r,e,t,n,a))})),j=v(((e,r,t,a,n,l)=>{if(!u(r,t))return y(e,t,l);var f=p(r,t,a);return o(n,f)||b(d(r,t,f,n)),i(r[t][f])})),w={},S={12:()=>j("default","@lumino/disposable",!1,[1,2,0,0]),88:()=>j("default","@jupyterlab/notebook",!1,[1,4,4,3]),256:()=>j("default","@lumino/widgets",!1,[1,2,3,1,,"alpha",0]),262:()=>j("default","@lumino/coreutils",!1,[1,2,0,0]),484:()=>j("default","@jupyterlab/ui-components",!1,[1,4,4,3]),542:()=>j("default","@jupyterlab/settingregistry",!1,[1,4,4,3]),560:()=>j("default","@jupyterlab/apputils",!1,[1,4,5,3]),689:()=>j("default","@jupyterlab/coreutils",!1,[1,6,4,3]),918:()=>j("default","@jupyterlab/translation",!1,[1,4,4,3]),186:()=>j("default","@jupyterlab/services",!1,[1,7,4,3]),313:()=>j("default","@jupyterlab/filebrowser",!1,[1,4,4,3]),424:()=>j("default","@jupyterlab/docmanager",!1,[1,4,4,3]),577:()=>j("default","@jupyterlab/mainmenu",!1,[1,4,4,3]),607:()=>j("default","@jupyterlab/launcher",!1,[1,4,4,3]),767:()=>j("default","@jupyterlab/rendermime",!1,[1,4,4,3]),865:()=>g("default","buffer",!1,[1,6,0,3],(()=>T.e(48).then((()=>()=>T(48))))),963:()=>j("default","@jupyterlab/codeeditor",!1,[1,4,4,3]),966:()=>j("default","jupyterlab-rise",!1,[2,0,43,1],(()=>Promise.all([T.e(720),T.e(88)]).then((()=>()=>T(720))))),993:()=>j("default","@jupyterlab/codemirror",!1,[1,4,4,3]),53:()=>j("default","@lumino/algorithm",!1,[1,2,0,0]),375:()=>m("default","@jupyterlab/docregistry",!1,[1,4,4,3]),549:()=>j("default","@jupyterlab/application",!1,[1,4,4,3]),602:()=>j("default","@lumino/signaling",!1,[1,2,0,0])},P={25:[12,88,256,262,484,542,560,689,918],88:[53,375,549,602],509:[186,313,424,577,607,767,865,963,966,993]},x={},T.f.consumes=(e,r)=>{T.o(P,e)&&P[e].forEach((e=>{if(T.o(w,e))return r.push(w[e]);if(!x[e]){var t=r=>{w[e]=0,T.m[e]=t=>{delete T.c[e],t.exports=r()}};x[e]=!0;var a=r=>{delete w[e],T.m[e]=t=>{throw delete T.c[e],r}};try{var n=S[e]();n.then?r.push(w[e]=n.then(t).catch(a)):t(n)}catch(e){a(e)}}}))},(()=>{var e={423:0};T.f.j=(r,t)=>{var a=T.o(e,r)?e[r]:void 0;if(0!==a)if(a)t.push(a[2]);else if(/^(25|88)$/.test(r))e[r]=0;else{var n=new Promise(((t,n)=>a=e[r]=[t,n]));t.push(a[2]=n);var o=T.p+T.u(r),u=new Error;T.l(o,(t=>{if(T.o(e,r)&&(0!==(a=e[r])&&(e[r]=void 0),a)){var n=t&&("load"===t.type?"missing":t.type),o=t&&t.target&&t.target.src;u.message="Loading chunk "+r+" failed.\n("+n+": "+o+")",u.name="ChunkLoadError",u.type=n,u.request=o,a[1](u)}}),"chunk-"+r,r)}};var r=(r,t)=>{var a,n,[o,u,i]=t,l=0;if(o.some((r=>0!==e[r]))){for(a in u)T.o(u,a)&&(T.m[a]=u[a]);i&&i(T)}for(r&&r(t);l<o.length;l++)n=o[l],T.o(e,n)&&e[n]&&e[n][0](),e[n]=0},t=self.webpackChunkjupyterlab_jupytext=self.webpackChunkjupyterlab_jupytext||[];t.forEach(r.bind(null,0)),t.push=r.bind(null,t.push.bind(t))})();var O=T(139);(_JUPYTERLAB=void 0===_JUPYTERLAB?{}:_JUPYTERLAB)["jupyterlab-jupytext"]=O})();