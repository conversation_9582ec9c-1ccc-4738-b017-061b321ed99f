"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[898],{19163:(t,e,a)=>{a.d(e,{S:()=>n});var r=a(75905);function n(t,e){if(t.accDescr){e.setAccDescription?.(t.accDescr)}if(t.accTitle){e.setAccTitle?.(t.accTitle)}if(t.title){e.setDiagramTitle?.(t.title)}}(0,r.K2)(n,"populateCommonDb")},80898:(t,e,a)=>{a.d(e,{diagram:()=>_});var r=a(19163);var n=a(96049);var s=a(93113);var i=a(75905);var o=a(24010);var c={showLegend:true,ticks:5,max:null,min:0,graticule:"circle"};var l={axes:[],curves:[],options:c};var d=structuredClone(l);var p=i.UI.radar;var g=(0,i.K2)((()=>{const t=(0,n.$t)({...p,...(0,i.zj)().radar});return t}),"getConfig");var u=(0,i.K2)((()=>d.axes),"getAxes");var h=(0,i.K2)((()=>d.curves),"getCurves");var x=(0,i.K2)((()=>d.options),"getOptions");var m=(0,i.K2)((t=>{d.axes=t.map((t=>({name:t.name,label:t.label??t.name})))}),"setAxes");var v=(0,i.K2)((t=>{d.curves=t.map((t=>({name:t.name,label:t.label??t.name,entries:$(t.entries)})))}),"setCurves");var $=(0,i.K2)((t=>{if(t[0].axis==void 0){return t.map((t=>t.value))}const e=u();if(e.length===0){throw new Error("Axes must be populated before curves for reference entries")}return e.map((e=>{const a=t.find((t=>t.axis?.$refText===e.name));if(a===void 0){throw new Error("Missing entry for axis "+e.label)}return a.value}))}),"computeCurveEntries");var f=(0,i.K2)((t=>{const e=t.reduce(((t,e)=>{t[e.name]=e;return t}),{});d.options={showLegend:e.showLegend?.value??c.showLegend,ticks:e.ticks?.value??c.ticks,max:e.max?.value??c.max,min:e.min?.value??c.min,graticule:e.graticule?.value??c.graticule}}),"setOptions");var y=(0,i.K2)((()=>{(0,i.IU)();d=structuredClone(l)}),"clear");var b={getAxes:u,getCurves:h,getOptions:x,setAxes:m,setCurves:v,setOptions:f,getConfig:g,clear:y,setAccTitle:i.SV,getAccTitle:i.iN,setDiagramTitle:i.ke,getDiagramTitle:i.ab,getAccDescription:i.m7,setAccDescription:i.EI};var w=(0,i.K2)((t=>{(0,r.S)(t,b);const{axes:e,curves:a,options:n}=t;b.setAxes(e);b.setCurves(a);b.setOptions(n)}),"populate");var C={parse:(0,i.K2)((async t=>{const e=await(0,o.qg)("radar",t);i.Rm.debug(e);w(e)}),"parse")};var M=(0,i.K2)(((t,e,a,r)=>{const n=r.db;const i=n.getAxes();const o=n.getCurves();const c=n.getOptions();const l=n.getConfig();const d=n.getDiagramTitle();const p=(0,s.D)(e);const g=K(p,l);const u=c.max??Math.max(...o.map((t=>Math.max(...t.entries))));const h=c.min;const x=Math.min(l.width,l.height)/2;L(g,i,x,c.ticks,c.graticule);T(g,i,x,l);k(g,i,o,h,u,c.graticule,l);S(g,o,c.showLegend,l);g.append("text").attr("class","radarTitle").text(d).attr("x",0).attr("y",-l.height/2-l.marginTop)}),"draw");var K=(0,i.K2)(((t,e)=>{const a=e.width+e.marginLeft+e.marginRight;const r=e.height+e.marginTop+e.marginBottom;const n={x:e.marginLeft+e.width/2,y:e.marginTop+e.height/2};t.attr("viewbox",`0 0 ${a} ${r}`).attr("width",a).attr("height",r);return t.append("g").attr("transform",`translate(${n.x}, ${n.y})`)}),"drawFrame");var L=(0,i.K2)(((t,e,a,r,n)=>{if(n==="circle"){for(let e=0;e<r;e++){const n=a*(e+1)/r;t.append("circle").attr("r",n).attr("class","radarGraticule")}}else if(n==="polygon"){const n=e.length;for(let s=0;s<r;s++){const i=a*(s+1)/r;const o=e.map(((t,e)=>{const a=2*e*Math.PI/n-Math.PI/2;const r=i*Math.cos(a);const s=i*Math.sin(a);return`${r},${s}`})).join(" ");t.append("polygon").attr("points",o).attr("class","radarGraticule")}}}),"drawGraticule");var T=(0,i.K2)(((t,e,a,r)=>{const n=e.length;for(let s=0;s<n;s++){const i=e[s].label;const o=2*s*Math.PI/n-Math.PI/2;t.append("line").attr("x1",0).attr("y1",0).attr("x2",a*r.axisScaleFactor*Math.cos(o)).attr("y2",a*r.axisScaleFactor*Math.sin(o)).attr("class","radarAxisLine");t.append("text").text(i).attr("x",a*r.axisLabelFactor*Math.cos(o)).attr("y",a*r.axisLabelFactor*Math.sin(o)).attr("class","radarAxisLabel")}}),"drawAxes");function k(t,e,a,r,n,s,i){const o=e.length;const c=Math.min(i.width,i.height)/2;a.forEach(((e,a)=>{if(e.entries.length!==o){return}const l=e.entries.map(((t,e)=>{const a=2*Math.PI*e/o-Math.PI/2;const s=A(t,r,n,c);const i=s*Math.cos(a);const l=s*Math.sin(a);return{x:i,y:l}}));if(s==="circle"){t.append("path").attr("d",O(l,i.curveTension)).attr("class",`radarCurve-${a}`)}else if(s==="polygon"){t.append("polygon").attr("points",l.map((t=>`${t.x},${t.y}`)).join(" ")).attr("class",`radarCurve-${a}`)}}))}(0,i.K2)(k,"drawCurves");function A(t,e,a,r){const n=Math.min(Math.max(t,e),a);return r*(n-e)/(a-e)}(0,i.K2)(A,"relativeRadius");function O(t,e){const a=t.length;let r=`M${t[0].x},${t[0].y}`;for(let n=0;n<a;n++){const s=t[(n-1+a)%a];const i=t[n];const o=t[(n+1)%a];const c=t[(n+2)%a];const l={x:i.x+(o.x-s.x)*e,y:i.y+(o.y-s.y)*e};const d={x:o.x-(c.x-i.x)*e,y:o.y-(c.y-i.y)*e};r+=` C${l.x},${l.y} ${d.x},${d.y} ${o.x},${o.y}`}return`${r} Z`}(0,i.K2)(O,"closedRoundCurve");function S(t,e,a,r){if(!a){return}const n=(r.width/2+r.marginRight)*3/4;const s=-(r.height/2+r.marginTop)*3/4;const i=20;e.forEach(((e,a)=>{const r=t.append("g").attr("transform",`translate(${n}, ${s+a*i})`);r.append("rect").attr("width",12).attr("height",12).attr("class",`radarLegendBox-${a}`);r.append("text").attr("x",16).attr("y",0).attr("class","radarLegendText").text(e.label)}))}(0,i.K2)(S,"drawLegend");var I={draw:M};var D=(0,i.K2)(((t,e)=>{let a="";for(let r=0;r<t.THEME_COLOR_LIMIT;r++){const n=t[`cScale${r}`];a+=`\n\t\t.radarCurve-${r} {\n\t\t\tcolor: ${n};\n\t\t\tfill: ${n};\n\t\t\tfill-opacity: ${e.curveOpacity};\n\t\t\tstroke: ${n};\n\t\t\tstroke-width: ${e.curveStrokeWidth};\n\t\t}\n\t\t.radarLegendBox-${r} {\n\t\t\tfill: ${n};\n\t\t\tfill-opacity: ${e.curveOpacity};\n\t\t\tstroke: ${n};\n\t\t}\n\t\t`}return a}),"genIndexStyles");var z=(0,i.K2)((t=>{const e=(0,i.P$)();const a=(0,i.zj)();const r=(0,n.$t)(e,a.themeVariables);const s=(0,n.$t)(r.radar,t);return{themeVariables:r,radarOptions:s}}),"buildRadarStyleOptions");var E=(0,i.K2)((({radar:t}={})=>{const{themeVariables:e,radarOptions:a}=z(t);return`\n\t.radarTitle {\n\t\tfont-size: ${e.fontSize};\n\t\tcolor: ${e.titleColor};\n\t\tdominant-baseline: hanging;\n\t\ttext-anchor: middle;\n\t}\n\t.radarAxisLine {\n\t\tstroke: ${a.axisColor};\n\t\tstroke-width: ${a.axisStrokeWidth};\n\t}\n\t.radarAxisLabel {\n\t\tdominant-baseline: middle;\n\t\ttext-anchor: middle;\n\t\tfont-size: ${a.axisLabelFontSize}px;\n\t\tcolor: ${a.axisColor};\n\t}\n\t.radarGraticule {\n\t\tfill: ${a.graticuleColor};\n\t\tfill-opacity: ${a.graticuleOpacity};\n\t\tstroke: ${a.graticuleColor};\n\t\tstroke-width: ${a.graticuleStrokeWidth};\n\t}\n\t.radarLegendText {\n\t\ttext-anchor: start;\n\t\tfont-size: ${a.legendFontSize}px;\n\t\tdominant-baseline: hanging;\n\t}\n\t${D(e,a)}\n\t`}),"styles");var _={parser:C,db:b,renderer:I,styles:E}}}]);