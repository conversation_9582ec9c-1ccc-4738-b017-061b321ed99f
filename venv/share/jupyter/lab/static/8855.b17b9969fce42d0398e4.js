"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[8855],{88855:(t,e,s)=>{s.d(e,{Zk:()=>l,q7:()=>Q,tM:()=>Tt,u4:()=>_t});var i=s(15051);var r=s(94065);var n=s(96049);var a=s(75905);var o=function(){var t=(0,a.K2)((function(t,e,s,i){for(s=s||{},i=t.length;i--;s[t[i]]=e);return s}),"o"),e=[1,2],s=[1,3],i=[1,4],r=[2,4],n=[1,9],o=[1,11],l=[1,16],c=[1,17],h=[1,18],d=[1,19],u=[1,32],p=[1,20],f=[1,21],y=[1,22],g=[1,23],m=[1,24],S=[1,26],b=[1,27],k=[1,28],_=[1,29],v=[1,30],T=[1,31],E=[1,34],D=[1,35],x=[1,36],C=[1,37],$=[1,33],I=[1,4,5,16,17,19,21,22,24,25,26,27,28,29,33,35,37,38,42,45,48,49,50,51,54],L=[1,4,5,14,15,16,17,19,21,22,24,25,26,27,28,29,33,35,37,38,42,45,48,49,50,51,54],A=[4,5,16,17,19,21,22,24,25,26,27,28,29,33,35,37,38,42,45,48,49,50,51,54];var w={trace:(0,a.K2)((function t(){}),"trace"),yy:{},symbols_:{error:2,start:3,SPACE:4,NL:5,SD:6,document:7,line:8,statement:9,classDefStatement:10,styleStatement:11,cssClassStatement:12,idStatement:13,DESCR:14,"--\x3e":15,HIDE_EMPTY:16,scale:17,WIDTH:18,COMPOSIT_STATE:19,STRUCT_START:20,STRUCT_STOP:21,STATE_DESCR:22,AS:23,ID:24,FORK:25,JOIN:26,CHOICE:27,CONCURRENT:28,note:29,notePosition:30,NOTE_TEXT:31,direction:32,acc_title:33,acc_title_value:34,acc_descr:35,acc_descr_value:36,acc_descr_multiline_value:37,classDef:38,CLASSDEF_ID:39,CLASSDEF_STYLEOPTS:40,DEFAULT:41,style:42,STYLE_IDS:43,STYLEDEF_STYLEOPTS:44,class:45,CLASSENTITY_IDS:46,STYLECLASS:47,direction_tb:48,direction_bt:49,direction_rl:50,direction_lr:51,eol:52,";":53,EDGE_STATE:54,STYLE_SEPARATOR:55,left_of:56,right_of:57,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NL",6:"SD",14:"DESCR",15:"--\x3e",16:"HIDE_EMPTY",17:"scale",18:"WIDTH",19:"COMPOSIT_STATE",20:"STRUCT_START",21:"STRUCT_STOP",22:"STATE_DESCR",23:"AS",24:"ID",25:"FORK",26:"JOIN",27:"CHOICE",28:"CONCURRENT",29:"note",31:"NOTE_TEXT",33:"acc_title",34:"acc_title_value",35:"acc_descr",36:"acc_descr_value",37:"acc_descr_multiline_value",38:"classDef",39:"CLASSDEF_ID",40:"CLASSDEF_STYLEOPTS",41:"DEFAULT",42:"style",43:"STYLE_IDS",44:"STYLEDEF_STYLEOPTS",45:"class",46:"CLASSENTITY_IDS",47:"STYLECLASS",48:"direction_tb",49:"direction_bt",50:"direction_rl",51:"direction_lr",53:";",54:"EDGE_STATE",55:"STYLE_SEPARATOR",56:"left_of",57:"right_of"},productions_:[0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[9,1],[9,1],[9,1],[9,1],[9,2],[9,3],[9,4],[9,1],[9,2],[9,1],[9,4],[9,3],[9,6],[9,1],[9,1],[9,1],[9,1],[9,4],[9,4],[9,1],[9,2],[9,2],[9,1],[10,3],[10,3],[11,3],[12,3],[32,1],[32,1],[32,1],[32,1],[52,1],[52,1],[13,1],[13,1],[13,3],[13,3],[30,1],[30,1]],performAction:(0,a.K2)((function t(e,s,i,r,n,a,o){var l=a.length-1;switch(n){case 3:r.setRootDoc(a[l]);return a[l];break;case 4:this.$=[];break;case 5:if(a[l]!="nl"){a[l-1].push(a[l]);this.$=a[l-1]}break;case 6:case 7:this.$=a[l];break;case 8:this.$="nl";break;case 12:this.$=a[l];break;case 13:const t=a[l-1];t.description=r.trimColon(a[l]);this.$=t;break;case 14:this.$={stmt:"relation",state1:a[l-2],state2:a[l]};break;case 15:const e=r.trimColon(a[l]);this.$={stmt:"relation",state1:a[l-3],state2:a[l-1],description:e};break;case 19:this.$={stmt:"state",id:a[l-3],type:"default",description:"",doc:a[l-1]};break;case 20:var c=a[l];var h=a[l-2].trim();if(a[l].match(":")){var d=a[l].split(":");c=d[0];h=[h,d[1]]}this.$={stmt:"state",id:c,type:"default",description:h};break;case 21:this.$={stmt:"state",id:a[l-3],type:"default",description:a[l-5],doc:a[l-1]};break;case 22:this.$={stmt:"state",id:a[l],type:"fork"};break;case 23:this.$={stmt:"state",id:a[l],type:"join"};break;case 24:this.$={stmt:"state",id:a[l],type:"choice"};break;case 25:this.$={stmt:"state",id:r.getDividerId(),type:"divider"};break;case 26:this.$={stmt:"state",id:a[l-1].trim(),note:{position:a[l-2].trim(),text:a[l].trim()}};break;case 29:this.$=a[l].trim();r.setAccTitle(this.$);break;case 30:case 31:this.$=a[l].trim();r.setAccDescription(this.$);break;case 32:case 33:this.$={stmt:"classDef",id:a[l-1].trim(),classes:a[l].trim()};break;case 34:this.$={stmt:"style",id:a[l-1].trim(),styleClass:a[l].trim()};break;case 35:this.$={stmt:"applyClass",id:a[l-1].trim(),styleClass:a[l].trim()};break;case 36:r.setDirection("TB");this.$={stmt:"dir",value:"TB"};break;case 37:r.setDirection("BT");this.$={stmt:"dir",value:"BT"};break;case 38:r.setDirection("RL");this.$={stmt:"dir",value:"RL"};break;case 39:r.setDirection("LR");this.$={stmt:"dir",value:"LR"};break;case 42:case 43:this.$={stmt:"state",id:a[l].trim(),type:"default",description:""};break;case 44:this.$={stmt:"state",id:a[l-2].trim(),classes:[a[l].trim()],type:"default",description:""};break;case 45:this.$={stmt:"state",id:a[l-2].trim(),classes:[a[l].trim()],type:"default",description:""};break}}),"anonymous"),table:[{3:1,4:e,5:s,6:i},{1:[3]},{3:5,4:e,5:s,6:i},{3:6,4:e,5:s,6:i},t([1,4,5,16,17,19,22,24,25,26,27,28,29,33,35,37,38,42,45,48,49,50,51,54],r,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:n,5:o,8:8,9:10,10:12,11:13,12:14,13:15,16:l,17:c,19:h,22:d,24:u,25:p,26:f,27:y,28:g,29:m,32:25,33:S,35:b,37:k,38:_,42:v,45:T,48:E,49:D,50:x,51:C,54:$},t(I,[2,5]),{9:38,10:12,11:13,12:14,13:15,16:l,17:c,19:h,22:d,24:u,25:p,26:f,27:y,28:g,29:m,32:25,33:S,35:b,37:k,38:_,42:v,45:T,48:E,49:D,50:x,51:C,54:$},t(I,[2,7]),t(I,[2,8]),t(I,[2,9]),t(I,[2,10]),t(I,[2,11]),t(I,[2,12],{14:[1,39],15:[1,40]}),t(I,[2,16]),{18:[1,41]},t(I,[2,18],{20:[1,42]}),{23:[1,43]},t(I,[2,22]),t(I,[2,23]),t(I,[2,24]),t(I,[2,25]),{30:44,31:[1,45],56:[1,46],57:[1,47]},t(I,[2,28]),{34:[1,48]},{36:[1,49]},t(I,[2,31]),{39:[1,50],41:[1,51]},{43:[1,52]},{46:[1,53]},t(L,[2,42],{55:[1,54]}),t(L,[2,43],{55:[1,55]}),t(I,[2,36]),t(I,[2,37]),t(I,[2,38]),t(I,[2,39]),t(I,[2,6]),t(I,[2,13]),{13:56,24:u,54:$},t(I,[2,17]),t(A,r,{7:57}),{24:[1,58]},{24:[1,59]},{23:[1,60]},{24:[2,46]},{24:[2,47]},t(I,[2,29]),t(I,[2,30]),{40:[1,61]},{40:[1,62]},{44:[1,63]},{47:[1,64]},{24:[1,65]},{24:[1,66]},t(I,[2,14],{14:[1,67]}),{4:n,5:o,8:8,9:10,10:12,11:13,12:14,13:15,16:l,17:c,19:h,21:[1,68],22:d,24:u,25:p,26:f,27:y,28:g,29:m,32:25,33:S,35:b,37:k,38:_,42:v,45:T,48:E,49:D,50:x,51:C,54:$},t(I,[2,20],{20:[1,69]}),{31:[1,70]},{24:[1,71]},t(I,[2,32]),t(I,[2,33]),t(I,[2,34]),t(I,[2,35]),t(L,[2,44]),t(L,[2,45]),t(I,[2,15]),t(I,[2,19]),t(A,r,{7:72}),t(I,[2,26]),t(I,[2,27]),{4:n,5:o,8:8,9:10,10:12,11:13,12:14,13:15,16:l,17:c,19:h,21:[1,73],22:d,24:u,25:p,26:f,27:y,28:g,29:m,32:25,33:S,35:b,37:k,38:_,42:v,45:T,48:E,49:D,50:x,51:C,54:$},t(I,[2,21])],defaultActions:{5:[2,1],6:[2,2],46:[2,46],47:[2,47]},parseError:(0,a.K2)((function t(e,s){if(s.recoverable){this.trace(e)}else{var i=new Error(e);i.hash=s;throw i}}),"parseError"),parse:(0,a.K2)((function t(e){var s=this,i=[0],r=[],n=[null],o=[],l=this.table,c="",h=0,d=0,u=0,p=2,f=1;var y=o.slice.call(arguments,1);var g=Object.create(this.lexer);var m={yy:{}};for(var S in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,S)){m.yy[S]=this.yy[S]}}g.setInput(e,m.yy);m.yy.lexer=g;m.yy.parser=this;if(typeof g.yylloc=="undefined"){g.yylloc={}}var b=g.yylloc;o.push(b);var k=g.options&&g.options.ranges;if(typeof m.yy.parseError==="function"){this.parseError=m.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function _(t){i.length=i.length-2*t;n.length=n.length-t;o.length=o.length-t}(0,a.K2)(_,"popStack");function v(){var t;t=r.pop()||g.lex()||f;if(typeof t!=="number"){if(t instanceof Array){r=t;t=r.pop()}t=s.symbols_[t]||t}return t}(0,a.K2)(v,"lex");var T,E,D,x,C,$,I={},L,A,w,R;while(true){D=i[i.length-1];if(this.defaultActions[D]){x=this.defaultActions[D]}else{if(T===null||typeof T=="undefined"){T=v()}x=l[D]&&l[D][T]}if(typeof x==="undefined"||!x.length||!x[0]){var O="";R=[];for(L in l[D]){if(this.terminals_[L]&&L>p){R.push("'"+this.terminals_[L]+"'")}}if(g.showPosition){O="Parse error on line "+(h+1)+":\n"+g.showPosition()+"\nExpecting "+R.join(", ")+", got '"+(this.terminals_[T]||T)+"'"}else{O="Parse error on line "+(h+1)+": Unexpected "+(T==f?"end of input":"'"+(this.terminals_[T]||T)+"'")}this.parseError(O,{text:g.match,token:this.terminals_[T]||T,line:g.yylineno,loc:b,expected:R})}if(x[0]instanceof Array&&x.length>1){throw new Error("Parse Error: multiple actions possible at state: "+D+", token: "+T)}switch(x[0]){case 1:i.push(T);n.push(g.yytext);o.push(g.yylloc);i.push(x[1]);T=null;if(!E){d=g.yyleng;c=g.yytext;h=g.yylineno;b=g.yylloc;if(u>0){u--}}else{T=E;E=null}break;case 2:A=this.productions_[x[1]][1];I.$=n[n.length-A];I._$={first_line:o[o.length-(A||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-(A||1)].first_column,last_column:o[o.length-1].last_column};if(k){I._$.range=[o[o.length-(A||1)].range[0],o[o.length-1].range[1]]}$=this.performAction.apply(I,[c,d,h,m.yy,x[1],n,o].concat(y));if(typeof $!=="undefined"){return $}if(A){i=i.slice(0,-1*A*2);n=n.slice(0,-1*A);o=o.slice(0,-1*A)}i.push(this.productions_[x[1]][0]);n.push(I.$);o.push(I._$);w=l[i[i.length-2]][i[i.length-1]];i.push(w);break;case 3:return true}}return true}),"parse")};var R=function(){var t={EOF:1,parseError:(0,a.K2)((function t(e,s){if(this.yy.parser){this.yy.parser.parseError(e,s)}else{throw new Error(e)}}),"parseError"),setInput:(0,a.K2)((function(t,e){this.yy=e||this.yy||{};this._input=t;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this}),"setInput"),input:(0,a.K2)((function(){var t=this._input[0];this.yytext+=t;this.yyleng++;this.offset++;this.match+=t;this.matched+=t;var e=t.match(/(?:\r\n?|\n).*/g);if(e){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return t}),"input"),unput:(0,a.K2)((function(t){var e=t.length;var s=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-e);this.offset-=e;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(s.length-1){this.yylineno-=s.length-1}var r=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:s?(s.length===i.length?this.yylloc.first_column:0)+i[i.length-s.length].length-s[0].length:this.yylloc.first_column-e};if(this.options.ranges){this.yylloc.range=[r[0],r[0]+this.yyleng-e]}this.yyleng=this.yytext.length;return this}),"unput"),more:(0,a.K2)((function(){this._more=true;return this}),"more"),reject:(0,a.K2)((function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this}),"reject"),less:(0,a.K2)((function(t){this.unput(this.match.slice(t))}),"less"),pastInput:(0,a.K2)((function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,a.K2)((function(){var t=this.match;if(t.length<20){t+=this._input.substr(0,20-t.length)}return(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,a.K2)((function(){var t=this.pastInput();var e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"}),"showPosition"),test_match:(0,a.K2)((function(t,e){var s,i,r;if(this.options.backtrack_lexer){r={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){r.yylloc.range=this.yylloc.range.slice(0)}}i=t[0].match(/(?:\r\n?|\n).*/g);if(i){this.yylineno+=i.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length};this.yytext+=t[0];this.match+=t[0];this.matches=t;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(t[0].length);this.matched+=t[0];s=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(s){return s}else if(this._backtrack){for(var n in r){this[n]=r[n]}return false}return false}),"test_match"),next:(0,a.K2)((function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var t,e,s,i;if(!this._more){this.yytext="";this.match=""}var r=this._currentRules();for(var n=0;n<r.length;n++){s=this._input.match(this.rules[r[n]]);if(s&&(!e||s[0].length>e[0].length)){e=s;i=n;if(this.options.backtrack_lexer){t=this.test_match(s,r[n]);if(t!==false){return t}else if(this._backtrack){e=false;continue}else{return false}}else if(!this.options.flex){break}}}if(e){t=this.test_match(e,r[i]);if(t!==false){return t}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}}),"next"),lex:(0,a.K2)((function t(){var e=this.next();if(e){return e}else{return this.lex()}}),"lex"),begin:(0,a.K2)((function t(e){this.conditionStack.push(e)}),"begin"),popState:(0,a.K2)((function t(){var e=this.conditionStack.length-1;if(e>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}}),"popState"),_currentRules:(0,a.K2)((function t(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}}),"_currentRules"),topState:(0,a.K2)((function t(e){e=this.conditionStack.length-1-Math.abs(e||0);if(e>=0){return this.conditionStack[e]}else{return"INITIAL"}}),"topState"),pushState:(0,a.K2)((function t(e){this.begin(e)}),"pushState"),stateStackSize:(0,a.K2)((function t(){return this.conditionStack.length}),"stateStackSize"),options:{"case-insensitive":true},performAction:(0,a.K2)((function t(e,s,i,r){var n=r;switch(i){case 0:return 41;break;case 1:return 48;break;case 2:return 49;break;case 3:return 50;break;case 4:return 51;break;case 5:break;case 6:{}break;case 7:return 5;break;case 8:break;case 9:break;case 10:break;case 11:break;case 12:this.pushState("SCALE");return 17;break;case 13:return 18;break;case 14:this.popState();break;case 15:this.begin("acc_title");return 33;break;case 16:this.popState();return"acc_title_value";break;case 17:this.begin("acc_descr");return 35;break;case 18:this.popState();return"acc_descr_value";break;case 19:this.begin("acc_descr_multiline");break;case 20:this.popState();break;case 21:return"acc_descr_multiline_value";break;case 22:this.pushState("CLASSDEF");return 38;break;case 23:this.popState();this.pushState("CLASSDEFID");return"DEFAULT_CLASSDEF_ID";break;case 24:this.popState();this.pushState("CLASSDEFID");return 39;break;case 25:this.popState();return 40;break;case 26:this.pushState("CLASS");return 45;break;case 27:this.popState();this.pushState("CLASS_STYLE");return 46;break;case 28:this.popState();return 47;break;case 29:this.pushState("STYLE");return 42;break;case 30:this.popState();this.pushState("STYLEDEF_STYLES");return 43;break;case 31:this.popState();return 44;break;case 32:this.pushState("SCALE");return 17;break;case 33:return 18;break;case 34:this.popState();break;case 35:this.pushState("STATE");break;case 36:this.popState();s.yytext=s.yytext.slice(0,-8).trim();return 25;break;case 37:this.popState();s.yytext=s.yytext.slice(0,-8).trim();return 26;break;case 38:this.popState();s.yytext=s.yytext.slice(0,-10).trim();return 27;break;case 39:this.popState();s.yytext=s.yytext.slice(0,-8).trim();return 25;break;case 40:this.popState();s.yytext=s.yytext.slice(0,-8).trim();return 26;break;case 41:this.popState();s.yytext=s.yytext.slice(0,-10).trim();return 27;break;case 42:return 48;break;case 43:return 49;break;case 44:return 50;break;case 45:return 51;break;case 46:this.pushState("STATE_STRING");break;case 47:this.pushState("STATE_ID");return"AS";break;case 48:this.popState();return"ID";break;case 49:this.popState();break;case 50:return"STATE_DESCR";break;case 51:return 19;break;case 52:this.popState();break;case 53:this.popState();this.pushState("struct");return 20;break;case 54:break;case 55:this.popState();return 21;break;case 56:break;case 57:this.begin("NOTE");return 29;break;case 58:this.popState();this.pushState("NOTE_ID");return 56;break;case 59:this.popState();this.pushState("NOTE_ID");return 57;break;case 60:this.popState();this.pushState("FLOATING_NOTE");break;case 61:this.popState();this.pushState("FLOATING_NOTE_ID");return"AS";break;case 62:break;case 63:return"NOTE_TEXT";break;case 64:this.popState();return"ID";break;case 65:this.popState();this.pushState("NOTE_TEXT");return 24;break;case 66:this.popState();s.yytext=s.yytext.substr(2).trim();return 31;break;case 67:this.popState();s.yytext=s.yytext.slice(0,-8).trim();return 31;break;case 68:return 6;break;case 69:return 6;break;case 70:return 16;break;case 71:return 54;break;case 72:return 24;break;case 73:s.yytext=s.yytext.trim();return 14;break;case 74:return 15;break;case 75:return 28;break;case 76:return 55;break;case 77:return 5;break;case 78:return"INVALID";break}}),"anonymous"),rules:[/^(?:default\b)/i,/^(?:.*direction\s+TB[^\n]*)/i,/^(?:.*direction\s+BT[^\n]*)/i,/^(?:.*direction\s+RL[^\n]*)/i,/^(?:.*direction\s+LR[^\n]*)/i,/^(?:%%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n]+)/i,/^(?:[\s]+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%[^\n]*)/i,/^(?:scale\s+)/i,/^(?:\d+)/i,/^(?:\s+width\b)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:classDef\s+)/i,/^(?:DEFAULT\s+)/i,/^(?:\w+\s+)/i,/^(?:[^\n]*)/i,/^(?:class\s+)/i,/^(?:(\w+)+((,\s*\w+)*))/i,/^(?:[^\n]*)/i,/^(?:style\s+)/i,/^(?:[\w,]+\s+)/i,/^(?:[^\n]*)/i,/^(?:scale\s+)/i,/^(?:\d+)/i,/^(?:\s+width\b)/i,/^(?:state\s+)/i,/^(?:.*<<fork>>)/i,/^(?:.*<<join>>)/i,/^(?:.*<<choice>>)/i,/^(?:.*\[\[fork\]\])/i,/^(?:.*\[\[join\]\])/i,/^(?:.*\[\[choice\]\])/i,/^(?:.*direction\s+TB[^\n]*)/i,/^(?:.*direction\s+BT[^\n]*)/i,/^(?:.*direction\s+RL[^\n]*)/i,/^(?:.*direction\s+LR[^\n]*)/i,/^(?:["])/i,/^(?:\s*as\s+)/i,/^(?:[^\n\{]*)/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:[^\n\s\{]+)/i,/^(?:\n)/i,/^(?:\{)/i,/^(?:%%(?!\{)[^\n]*)/i,/^(?:\})/i,/^(?:[\n])/i,/^(?:note\s+)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:")/i,/^(?:\s*as\s*)/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:[^\n]*)/i,/^(?:\s*[^:\n\s\-]+)/i,/^(?:\s*:[^:\n;]+)/i,/^(?:[\s\S]*?end note\b)/i,/^(?:stateDiagram\s+)/i,/^(?:stateDiagram-v2\s+)/i,/^(?:hide empty description\b)/i,/^(?:\[\*\])/i,/^(?:[^:\n\s\-\{]+)/i,/^(?:\s*:[^:\n;]+)/i,/^(?:-->)/i,/^(?:--)/i,/^(?::::)/i,/^(?:$)/i,/^(?:.)/i],conditions:{LINE:{rules:[9,10],inclusive:false},struct:{rules:[9,10,22,26,29,35,42,43,44,45,54,55,56,57,71,72,73,74,75],inclusive:false},FLOATING_NOTE_ID:{rules:[64],inclusive:false},FLOATING_NOTE:{rules:[61,62,63],inclusive:false},NOTE_TEXT:{rules:[66,67],inclusive:false},NOTE_ID:{rules:[65],inclusive:false},NOTE:{rules:[58,59,60],inclusive:false},STYLEDEF_STYLEOPTS:{rules:[],inclusive:false},STYLEDEF_STYLES:{rules:[31],inclusive:false},STYLE_IDS:{rules:[],inclusive:false},STYLE:{rules:[30],inclusive:false},CLASS_STYLE:{rules:[28],inclusive:false},CLASS:{rules:[27],inclusive:false},CLASSDEFID:{rules:[25],inclusive:false},CLASSDEF:{rules:[23,24],inclusive:false},acc_descr_multiline:{rules:[20,21],inclusive:false},acc_descr:{rules:[18],inclusive:false},acc_title:{rules:[16],inclusive:false},SCALE:{rules:[13,14,33,34],inclusive:false},ALIAS:{rules:[],inclusive:false},STATE_ID:{rules:[48],inclusive:false},STATE_STRING:{rules:[49,50],inclusive:false},FORK_STATE:{rules:[],inclusive:false},STATE:{rules:[9,10,36,37,38,39,40,41,46,47,51,52,53],inclusive:false},ID:{rules:[9,10],inclusive:false},INITIAL:{rules:[0,1,2,3,4,5,6,7,8,10,11,12,15,17,19,22,26,29,32,35,53,57,68,69,70,71,72,73,74,76,77,78],inclusive:true}}};return t}();w.lexer=R;function O(){this.yy={}}(0,a.K2)(O,"Parser");O.prototype=w;w.Parser=O;return new O}();o.parser=o;var l=o;var c="TB";var h="TB";var d="dir";var u="state";var p="relation";var f="classDef";var y="style";var g="applyClass";var m="default";var S="divider";var b="fill:none";var k="fill: #333";var _="c";var v="text";var T="normal";var E="rect";var D="rectWithTitle";var x="stateStart";var C="stateEnd";var $="divider";var I="roundedWithTitle";var L="note";var A="noteGroup";var w="statediagram";var R="state";var O=`${w}-${R}`;var N="transition";var K="note";var B="note-edge";var F=`${N} ${B}`;var Y=`${w}-${K}`;var P="cluster";var G=`${w}-${P}`;var j="cluster-alt";var z=`${w}-${j}`;var U="parent";var M="note";var V="state";var X="----";var W=`${X}${M}`;var H=`${X}${U}`;var J=(0,a.K2)(((t,e=h)=>{if(!t.doc){return e}let s=e;for(const i of t.doc){if(i.stmt==="dir"){s=i.value}}return s}),"getDir");var q=(0,a.K2)((function(t,e){return e.db.getClasses()}),"getClasses");var Z=(0,a.K2)((async function(t,e,s,o){a.Rm.info("REF0:");a.Rm.info("Drawing state diagram (v2)",e);const{securityLevel:l,state:c,layout:h}=(0,a.D7)();o.db.extract(o.db.getRootDocV2());const d=o.db.getData();const u=(0,i.A)(e,l);d.type=o.type;d.layoutAlgorithm=h;d.nodeSpacing=c?.nodeSpacing||50;d.rankSpacing=c?.rankSpacing||50;d.markers=["barb"];d.diagramId=e;await(0,r.XX)(d,u);const p=8;n._K.insertTitle(u,"statediagramTitleText",c?.titleTopMargin??25,o.db.getDiagramTitle());(0,i.P)(u,p,w,c?.useMaxWidth??true)}),"draw");var Q={getClasses:q,draw:Z,getDir:J};var tt=new Map;var et=0;function st(t="",e=0,s="",i=X){const r=s!==null&&s.length>0?`${i}${s}`:"";return`${V}-${t}${r}-${e}`}(0,a.K2)(st,"stateDomId");var it=(0,a.K2)(((t,e,s,i,r,n,o,l)=>{a.Rm.trace("items",e);e.forEach((e=>{switch(e.stmt){case u:lt(t,e,s,i,r,n,o,l);break;case m:lt(t,e,s,i,r,n,o,l);break;case p:{lt(t,e.state1,s,i,r,n,o,l);lt(t,e.state2,s,i,r,n,o,l);const c={id:"edge"+et,start:e.state1.id,end:e.state2.id,arrowhead:"normal",arrowTypeEnd:"arrow_barb",style:b,labelStyle:"",label:a.Y2.sanitizeText(e.description,(0,a.D7)()),arrowheadStyle:k,labelpos:_,labelType:v,thickness:T,classes:N,look:o};r.push(c);et++}break}}))}),"setupDoc");var rt=(0,a.K2)(((t,e=h)=>{let s=e;if(t.doc){for(const e of t.doc){if(e.stmt==="dir"){s=e.value}}}return s}),"getDir");function nt(t,e,s){if(!e.id||e.id==="</join></fork>"||e.id==="</choice>"){return}if(e.cssClasses){if(!Array.isArray(e.cssCompiledStyles)){e.cssCompiledStyles=[]}e.cssClasses.split(" ").forEach((t=>{if(s.get(t)){const i=s.get(t);e.cssCompiledStyles=[...e.cssCompiledStyles,...i.styles]}}))}const i=t.find((t=>t.id===e.id));if(i){Object.assign(i,e)}else{t.push(e)}}(0,a.K2)(nt,"insertOrUpdateNode");function at(t){return t?.classes?.join(" ")??""}(0,a.K2)(at,"getClassesFromDbInfo");function ot(t){return t?.styles??[]}(0,a.K2)(ot,"getStylesFromDbInfo");var lt=(0,a.K2)(((t,e,s,i,r,n,o,l)=>{const c=e.id;const h=s.get(c);const d=at(h);const u=ot(h);a.Rm.info("dataFetcher parsedItem",e,h,u);if(c!=="root"){let s=E;if(e.start===true){s=x}else if(e.start===false){s=C}if(e.type!==m){s=e.type}if(!tt.get(c)){tt.set(c,{id:c,shape:s,description:a.Y2.sanitizeText(c,(0,a.D7)()),cssClasses:`${d} ${O}`,cssStyles:u})}const h=tt.get(c);if(e.description){if(Array.isArray(h.description)){h.shape=D;h.description.push(e.description)}else{if(h.description?.length>0){h.shape=D;if(h.description===c){h.description=[e.description]}else{h.description=[h.description,e.description]}}else{h.shape=E;h.description=e.description}}h.description=a.Y2.sanitizeTextOrArray(h.description,(0,a.D7)())}if(h.description?.length===1&&h.shape===D){if(h.type==="group"){h.shape=I}else{h.shape=E}}if(!h.type&&e.doc){a.Rm.info("Setting cluster for XCX",c,rt(e));h.type="group";h.isGroup=true;h.dir=rt(e);h.shape=e.type===S?$:I;h.cssClasses=`${h.cssClasses} ${G} ${n?z:""}`}const p={labelStyle:"",shape:h.shape,label:h.description,cssClasses:h.cssClasses,cssCompiledStyles:[],cssStyles:h.cssStyles,id:c,dir:h.dir,domId:st(c,et),type:h.type,isGroup:h.type==="group",padding:8,rx:10,ry:10,look:o};if(p.shape===$){p.label=""}if(t&&t.id!=="root"){a.Rm.trace("Setting node ",c," to be child of its parent ",t.id);p.parentId=t.id}p.centerLabel=true;if(e.note){const t={labelStyle:"",shape:L,label:e.note.text,cssClasses:Y,cssStyles:[],cssCompilesStyles:[],id:c+W+"-"+et,domId:st(c,et,M),type:h.type,isGroup:h.type==="group",padding:(0,a.D7)().flowchart.padding,look:o,position:e.note.position};const s=c+H;const n={labelStyle:"",shape:A,label:e.note.text,cssClasses:h.cssClasses,cssStyles:[],id:c+H,domId:st(c,et,U),type:"group",isGroup:true,padding:16,look:o,position:e.note.position};et++;n.id=s;t.parentId=s;nt(i,n,l);nt(i,t,l);nt(i,p,l);let d=c;let u=t.id;if(e.note.position==="left of"){d=t.id;u=c}r.push({id:d+"-"+u,start:d,end:u,arrowhead:"none",arrowTypeEnd:"",style:b,labelStyle:"",classes:F,arrowheadStyle:k,labelpos:_,labelType:v,thickness:T,look:o})}else{nt(i,p,l)}}if(e.doc){a.Rm.trace("Adding nodes children ");it(e,e.doc,s,i,r,!n,o,l)}}),"dataFetcher");var ct=(0,a.K2)((()=>{tt.clear();et=0}),"reset");var ht="[*]";var dt="start";var ut=ht;var pt="end";var ft="color";var yt="fill";var gt="bgFill";var mt=",";function St(){return new Map}(0,a.K2)(St,"newClassesList");var bt=(0,a.K2)((()=>({relations:[],states:new Map,documents:{}})),"newDoc");var kt=(0,a.K2)((t=>JSON.parse(JSON.stringify(t))),"clone");var _t=class{static{(0,a.K2)(this,"StateDB")}constructor(t){this.clear();this.version=t;this.setRootDoc=this.setRootDoc.bind(this);this.getDividerId=this.getDividerId.bind(this);this.setDirection=this.setDirection.bind(this);this.trimColon=this.trimColon.bind(this)}version;nodes=[];edges=[];rootDoc=[];classes=St();documents={root:bt()};currentDocument=this.documents.root;startEndCount=0;dividerCnt=0;static relationType={AGGREGATION:0,EXTENSION:1,COMPOSITION:2,DEPENDENCY:3};setRootDoc(t){a.Rm.info("Setting root doc",t);this.rootDoc=t;if(this.version===1){this.extract(t)}else{this.extract(this.getRootDocV2())}}getRootDoc(){return this.rootDoc}docTranslator(t,e,s){if(e.stmt===p){this.docTranslator(t,e.state1,true);this.docTranslator(t,e.state2,false)}else{if(e.stmt===u){if(e.id==="[*]"){e.id=s?t.id+"_start":t.id+"_end";e.start=s}else{e.id=e.id.trim()}}if(e.doc){const t=[];let s=[];let i;for(i=0;i<e.doc.length;i++){if(e.doc[i].type===S){const r=kt(e.doc[i]);r.doc=kt(s);t.push(r);s=[]}else{s.push(e.doc[i])}}if(t.length>0&&s.length>0){const i={stmt:u,id:(0,n.$C)(),type:"divider",doc:kt(s)};t.push(kt(i));e.doc=t}e.doc.forEach((t=>this.docTranslator(e,t,true)))}}}getRootDocV2(){this.docTranslator({id:"root"},{id:"root",doc:this.rootDoc},true);return{id:"root",doc:this.rootDoc}}extract(t){let e;if(t.doc){e=t.doc}else{e=t}a.Rm.info(e);this.clear(true);a.Rm.info("Extract initial document:",e);e.forEach((t=>{a.Rm.warn("Statement",t.stmt);switch(t.stmt){case u:this.addState(t.id.trim(),t.type,t.doc,t.description,t.note,t.classes,t.styles,t.textStyles);break;case p:this.addRelation(t.state1,t.state2,t.description);break;case f:this.addStyleClass(t.id.trim(),t.classes);break;case y:{const e=t.id.trim().split(",");const s=t.styleClass.split(",");e.forEach((t=>{let e=this.getState(t);if(e===void 0){const s=t.trim();this.addState(s);e=this.getState(s)}e.styles=s.map((t=>t.replace(/;/g,"")?.trim()))}))}break;case g:this.setCssClass(t.id.trim(),t.styleClass);break}}));const s=this.getStates();const i=(0,a.D7)();const r=i.look;ct();lt(void 0,this.getRootDocV2(),s,this.nodes,this.edges,true,r,this.classes);this.nodes.forEach((t=>{if(Array.isArray(t.label)){t.description=t.label.slice(1);if(t.isGroup&&t.description.length>0){throw new Error("Group nodes can only have label. Remove the additional description for node ["+t.id+"]")}t.label=t.label[0]}}))}addState(t,e=m,s=null,i=null,r=null,n=null,o=null,l=null){const c=t?.trim();if(!this.currentDocument.states.has(c)){a.Rm.info("Adding state ",c,i);this.currentDocument.states.set(c,{id:c,descriptions:[],type:e,doc:s,note:r,classes:[],styles:[],textStyles:[]})}else{if(!this.currentDocument.states.get(c).doc){this.currentDocument.states.get(c).doc=s}if(!this.currentDocument.states.get(c).type){this.currentDocument.states.get(c).type=e}}if(i){a.Rm.info("Setting state description",c,i);if(typeof i==="string"){this.addDescription(c,i.trim())}if(typeof i==="object"){i.forEach((t=>this.addDescription(c,t.trim())))}}if(r){const t=this.currentDocument.states.get(c);t.note=r;t.note.text=a.Y2.sanitizeText(t.note.text,(0,a.D7)())}if(n){a.Rm.info("Setting state classes",c,n);const t=typeof n==="string"?[n]:n;t.forEach((t=>this.setCssClass(c,t.trim())))}if(o){a.Rm.info("Setting state styles",c,o);const t=typeof o==="string"?[o]:o;t.forEach((t=>this.setStyle(c,t.trim())))}if(l){a.Rm.info("Setting state styles",c,o);const t=typeof l==="string"?[l]:l;t.forEach((t=>this.setTextStyle(c,t.trim())))}}clear(t){this.nodes=[];this.edges=[];this.documents={root:bt()};this.currentDocument=this.documents.root;this.startEndCount=0;this.classes=St();if(!t){(0,a.IU)()}}getState(t){return this.currentDocument.states.get(t)}getStates(){return this.currentDocument.states}logDocuments(){a.Rm.info("Documents = ",this.documents)}getRelations(){return this.currentDocument.relations}startIdIfNeeded(t=""){let e=t;if(t===ht){this.startEndCount++;e=`${dt}${this.startEndCount}`}return e}startTypeIfNeeded(t="",e=m){return t===ht?dt:e}endIdIfNeeded(t=""){let e=t;if(t===ut){this.startEndCount++;e=`${pt}${this.startEndCount}`}return e}endTypeIfNeeded(t="",e=m){return t===ut?pt:e}addRelationObjs(t,e,s){let i=this.startIdIfNeeded(t.id.trim());let r=this.startTypeIfNeeded(t.id.trim(),t.type);let n=this.startIdIfNeeded(e.id.trim());let o=this.startTypeIfNeeded(e.id.trim(),e.type);this.addState(i,r,t.doc,t.description,t.note,t.classes,t.styles,t.textStyles);this.addState(n,o,e.doc,e.description,e.note,e.classes,e.styles,e.textStyles);this.currentDocument.relations.push({id1:i,id2:n,relationTitle:a.Y2.sanitizeText(s,(0,a.D7)())})}addRelation(t,e,s){if(typeof t==="object"){this.addRelationObjs(t,e,s)}else{const i=this.startIdIfNeeded(t.trim());const r=this.startTypeIfNeeded(t);const n=this.endIdIfNeeded(e.trim());const o=this.endTypeIfNeeded(e);this.addState(i,r);this.addState(n,o);this.currentDocument.relations.push({id1:i,id2:n,title:a.Y2.sanitizeText(s,(0,a.D7)())})}}addDescription(t,e){const s=this.currentDocument.states.get(t);const i=e.startsWith(":")?e.replace(":","").trim():e;s.descriptions.push(a.Y2.sanitizeText(i,(0,a.D7)()))}cleanupLabel(t){if(t.substring(0,1)===":"){return t.substr(2).trim()}else{return t.trim()}}getDividerId(){this.dividerCnt++;return"divider-id-"+this.dividerCnt}addStyleClass(t,e=""){if(!this.classes.has(t)){this.classes.set(t,{id:t,styles:[],textStyles:[]})}const s=this.classes.get(t);if(e!==void 0&&e!==null){e.split(mt).forEach((t=>{const e=t.replace(/([^;]*);/,"$1").trim();if(RegExp(ft).exec(t)){const t=e.replace(yt,gt);const i=t.replace(ft,yt);s.textStyles.push(i)}s.styles.push(e)}))}}getClasses(){return this.classes}setCssClass(t,e){t.split(",").forEach((t=>{let s=this.getState(t);if(s===void 0){const e=t.trim();this.addState(e);s=this.getState(e)}s.classes.push(e)}))}setStyle(t,e){const s=this.getState(t);if(s!==void 0){s.styles.push(e)}}setTextStyle(t,e){const s=this.getState(t);if(s!==void 0){s.textStyles.push(e)}}getDirectionStatement(){return this.rootDoc.find((t=>t.stmt===d))}getDirection(){return this.getDirectionStatement()?.value??c}setDirection(t){const e=this.getDirectionStatement();if(e){e.value=t}else{this.rootDoc.unshift({stmt:d,value:t})}}trimColon(t){return t&&t[0]===":"?t.substr(1).trim():t.trim()}getData(){const t=(0,a.D7)();return{nodes:this.nodes,edges:this.edges,other:{},config:t,direction:J(this.getRootDocV2())}}getConfig(){return(0,a.D7)().state}getAccTitle=a.iN;setAccTitle=a.SV;getAccDescription=a.m7;setAccDescription=a.EI;setDiagramTitle=a.ke;getDiagramTitle=a.ab};var vt=(0,a.K2)((t=>`\ndefs #statediagram-barbEnd {\n    fill: ${t.transitionColor};\n    stroke: ${t.transitionColor};\n  }\ng.stateGroup text {\n  fill: ${t.nodeBorder};\n  stroke: none;\n  font-size: 10px;\n}\ng.stateGroup text {\n  fill: ${t.textColor};\n  stroke: none;\n  font-size: 10px;\n\n}\ng.stateGroup .state-title {\n  font-weight: bolder;\n  fill: ${t.stateLabelColor};\n}\n\ng.stateGroup rect {\n  fill: ${t.mainBkg};\n  stroke: ${t.nodeBorder};\n}\n\ng.stateGroup line {\n  stroke: ${t.lineColor};\n  stroke-width: 1;\n}\n\n.transition {\n  stroke: ${t.transitionColor};\n  stroke-width: 1;\n  fill: none;\n}\n\n.stateGroup .composit {\n  fill: ${t.background};\n  border-bottom: 1px\n}\n\n.stateGroup .alt-composit {\n  fill: #e0e0e0;\n  border-bottom: 1px\n}\n\n.state-note {\n  stroke: ${t.noteBorderColor};\n  fill: ${t.noteBkgColor};\n\n  text {\n    fill: ${t.noteTextColor};\n    stroke: none;\n    font-size: 10px;\n  }\n}\n\n.stateLabel .box {\n  stroke: none;\n  stroke-width: 0;\n  fill: ${t.mainBkg};\n  opacity: 0.5;\n}\n\n.edgeLabel .label rect {\n  fill: ${t.labelBackgroundColor};\n  opacity: 0.5;\n}\n.edgeLabel {\n  background-color: ${t.edgeLabelBackground};\n  p {\n    background-color: ${t.edgeLabelBackground};\n  }\n  rect {\n    opacity: 0.5;\n    background-color: ${t.edgeLabelBackground};\n    fill: ${t.edgeLabelBackground};\n  }\n  text-align: center;\n}\n.edgeLabel .label text {\n  fill: ${t.transitionLabelColor||t.tertiaryTextColor};\n}\n.label div .edgeLabel {\n  color: ${t.transitionLabelColor||t.tertiaryTextColor};\n}\n\n.stateLabel text {\n  fill: ${t.stateLabelColor};\n  font-size: 10px;\n  font-weight: bold;\n}\n\n.node circle.state-start {\n  fill: ${t.specialStateColor};\n  stroke: ${t.specialStateColor};\n}\n\n.node .fork-join {\n  fill: ${t.specialStateColor};\n  stroke: ${t.specialStateColor};\n}\n\n.node circle.state-end {\n  fill: ${t.innerEndBackground};\n  stroke: ${t.background};\n  stroke-width: 1.5\n}\n.end-state-inner {\n  fill: ${t.compositeBackground||t.background};\n  // stroke: ${t.background};\n  stroke-width: 1.5\n}\n\n.node rect {\n  fill: ${t.stateBkg||t.mainBkg};\n  stroke: ${t.stateBorder||t.nodeBorder};\n  stroke-width: 1px;\n}\n.node polygon {\n  fill: ${t.mainBkg};\n  stroke: ${t.stateBorder||t.nodeBorder};;\n  stroke-width: 1px;\n}\n#statediagram-barbEnd {\n  fill: ${t.lineColor};\n}\n\n.statediagram-cluster rect {\n  fill: ${t.compositeTitleBackground};\n  stroke: ${t.stateBorder||t.nodeBorder};\n  stroke-width: 1px;\n}\n\n.cluster-label, .nodeLabel {\n  color: ${t.stateLabelColor};\n  // line-height: 1;\n}\n\n.statediagram-cluster rect.outer {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state .divider {\n  stroke: ${t.stateBorder||t.nodeBorder};\n}\n\n.statediagram-state .title-state {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-cluster.statediagram-cluster .inner {\n  fill: ${t.compositeBackground||t.background};\n}\n.statediagram-cluster.statediagram-cluster-alt .inner {\n  fill: ${t.altBackground?t.altBackground:"#efefef"};\n}\n\n.statediagram-cluster .inner {\n  rx:0;\n  ry:0;\n}\n\n.statediagram-state rect.basic {\n  rx: 5px;\n  ry: 5px;\n}\n.statediagram-state rect.divider {\n  stroke-dasharray: 10,10;\n  fill: ${t.altBackground?t.altBackground:"#efefef"};\n}\n\n.note-edge {\n  stroke-dasharray: 5;\n}\n\n.statediagram-note rect {\n  fill: ${t.noteBkgColor};\n  stroke: ${t.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n.statediagram-note rect {\n  fill: ${t.noteBkgColor};\n  stroke: ${t.noteBorderColor};\n  stroke-width: 1px;\n  rx: 0;\n  ry: 0;\n}\n\n.statediagram-note text {\n  fill: ${t.noteTextColor};\n}\n\n.statediagram-note .nodeLabel {\n  color: ${t.noteTextColor};\n}\n.statediagram .edgeLabel {\n  color: red; // ${t.noteTextColor};\n}\n\n#dependencyStart, #dependencyEnd {\n  fill: ${t.lineColor};\n  stroke: ${t.lineColor};\n  stroke-width: 1;\n}\n\n.statediagramTitleText {\n  text-anchor: middle;\n  font-size: 18px;\n  fill: ${t.textColor};\n}\n`),"getStyles");var Tt=vt},15051:(t,e,s)=>{s.d(e,{A:()=>n,P:()=>a});var i=s(75905);var r=s(24982);var n=(0,i.K2)(((t,e)=>{let s;if(e==="sandbox"){s=(0,r.Ltv)("#i"+t)}const i=e==="sandbox"?(0,r.Ltv)(s.nodes()[0].contentDocument.body):(0,r.Ltv)("body");const n=i.select(`[id="${t}"]`);return n}),"getDiagramElement");var a=(0,i.K2)(((t,e,s,r)=>{t.attr("class",s);const{width:n,height:a,x:c,y:h}=o(t,e);(0,i.a$)(t,a,n,r);const d=l(c,h,n,a,e);t.attr("viewBox",d);i.Rm.debug(`viewBox configured: ${d} with padding: ${e}`)}),"setupViewPortForSVG");var o=(0,i.K2)(((t,e)=>{const s=t.node()?.getBBox()||{width:0,height:0,x:0,y:0};return{width:s.width+e*2,height:s.height+e*2,x:s.x,y:s.y}}),"calculateDimensionsWithPadding");var l=(0,i.K2)(((t,e,s,i,r)=>`${t-r} ${e-r} ${s} ${i}`),"createViewBox")}}]);