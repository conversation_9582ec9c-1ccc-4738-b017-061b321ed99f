def clz(arg0):
    ...


def popc(arg0):
    ...


def byte_perm(arg0, arg1, arg2):
    ...


def mulhi(arg0, arg1):
    ...


def mul24(arg0, arg1):
    ...


def brev(arg0):
    ...


def sad(arg0, arg1, arg2):
    ...


def abs(arg0):
    ...


def floor(arg0):
    ...


def rcp64h(arg0):
    ...


def rsqrt(arg0):
    ...


def ceil(arg0):
    ...


def trunc(arg0):
    ...


def exp2(arg0):
    ...


def saturatef(arg0):
    ...


def fma_rn(arg0, arg1, arg2):
    ...


def fma_rz(arg0, arg1, arg2):
    ...


def fma_rd(arg0, arg1, arg2):
    ...


def fma_ru(arg0, arg1, arg2):
    ...


def fast_dividef(arg0, arg1):
    ...


def div_rn(arg0, arg1):
    ...


def div_rz(arg0, arg1):
    ...


def div_rd(arg0, arg1):
    ...


def div_ru(arg0, arg1):
    ...


def rcp_rn(arg0):
    ...


def rcp_rz(arg0):
    ...


def rcp_rd(arg0):
    ...


def rcp_ru(arg0):
    ...


def sqrt_rn(arg0):
    ...


def sqrt_rz(arg0):
    ...


def sqrt_rd(arg0):
    ...


def sqrt_ru(arg0):
    ...


def sqrt(arg0):
    ...


def add_rn(arg0, arg1):
    ...


def add_rz(arg0, arg1):
    ...


def add_rd(arg0, arg1):
    ...


def add_ru(arg0, arg1):
    ...


def mul_rn(arg0, arg1):
    ...


def mul_rz(arg0, arg1):
    ...


def mul_rd(arg0, arg1):
    ...


def mul_ru(arg0, arg1):
    ...


def double2float_rn(arg0):
    ...


def double2float_rz(arg0):
    ...


def double2float_rd(arg0):
    ...


def double2float_ru(arg0):
    ...


def double2int_rn(arg0):
    ...


def double2int_rz(arg0):
    ...


def double2int_rd(arg0):
    ...


def double2int_ru(arg0):
    ...


def double2uint_rn(arg0):
    ...


def double2uint_rz(arg0):
    ...


def double2uint_rd(arg0):
    ...


def double2uint_ru(arg0):
    ...


def int2double_rn(arg0):
    ...


def uint2double_rn(arg0):
    ...


def float2int_rn(arg0):
    ...


def float2int_rz(arg0):
    ...


def float2int_rd(arg0):
    ...


def float2int_ru(arg0):
    ...


def float2uint_rn(arg0):
    ...


def float2uint_rz(arg0):
    ...


def float2uint_rd(arg0):
    ...


def float2uint_ru(arg0):
    ...


def int2float_rn(arg0):
    ...


def int2float_rz(arg0):
    ...


def int2float_rd(arg0):
    ...


def int2float_ru(arg0):
    ...


def uint2float_rn(arg0):
    ...


def uint2float_rz(arg0):
    ...


def uint2float_rd(arg0):
    ...


def uint2float_ru(arg0):
    ...


def hiloint2double(arg0, arg1):
    ...


def double2loint(arg0):
    ...


def double2hiint(arg0):
    ...


def float2ll_rn(arg0):
    ...


def float2ll_rz(arg0):
    ...


def float2ll_rd(arg0):
    ...


def float2ll_ru(arg0):
    ...


def float2ull_rn(arg0):
    ...


def float2ull_rz(arg0):
    ...


def float2ull_rd(arg0):
    ...


def float2ull_ru(arg0):
    ...


def double2ll_rn(arg0):
    ...


def double2ll_rz(arg0):
    ...


def double2ll_rd(arg0):
    ...


def double2ll_ru(arg0):
    ...


def double2ull_rn(arg0):
    ...


def double2ull_rz(arg0):
    ...


def double2ull_rd(arg0):
    ...


def double2ull_ru(arg0):
    ...


def ll2float_rn(arg0):
    ...


def ll2float_rz(arg0):
    ...


def ll2float_rd(arg0):
    ...


def ll2float_ru(arg0):
    ...


def ull2float_rn(arg0):
    ...


def ull2float_rz(arg0):
    ...


def ull2float_rd(arg0):
    ...


def ull2float_ru(arg0):
    ...


def ll2double_rn(arg0):
    ...


def ll2double_rz(arg0):
    ...


def ll2double_rd(arg0):
    ...


def ll2double_ru(arg0):
    ...


def ull2double_rn(arg0):
    ...


def ull2double_rz(arg0):
    ...


def ull2double_rd(arg0):
    ...


def ull2double_ru(arg0):
    ...


def int_as_float(arg0):
    ...


def float_as_int(arg0):
    ...


def uint_as_float(arg0):
    ...


def float_as_uint(arg0):
    ...


def longlong_as_double(arg0):
    ...


def double_as_longlong(arg0):
    ...


def fast_sinf(arg0):
    ...


def fast_cosf(arg0):
    ...


def fast_log2f(arg0):
    ...


def fast_logf(arg0):
    ...


def fast_expf(arg0):
    ...


def fast_tanf(arg0):
    ...


def fast_exp10f(arg0):
    ...


def fast_log10f(arg0):
    ...


def fast_powf(arg0, arg1):
    ...


def hadd(arg0, arg1):
    ...


def rhadd(arg0, arg1):
    ...


def sub_rn(arg0, arg1):
    ...


def sub_rz(arg0, arg1):
    ...


def sub_rd(arg0, arg1):
    ...


def sub_ru(arg0, arg1):
    ...


def rsqrt_rn(arg0):
    ...


def ffs(arg0):
    ...


def rint(arg0):
    ...


def llrint(arg0):
    ...


def nearbyint(arg0):
    ...


def isnan(arg0):
    ...


def signbit(arg0):
    ...


def copysign(arg0, arg1):
    ...


def finitef(arg0):
    ...


def isinf(arg0):
    ...


def nextafter(arg0, arg1):
    ...


def sin(arg0):
    ...


def cos(arg0):
    ...


def sinpi(arg0):
    ...


def cospi(arg0):
    ...


def tan(arg0):
    ...


def log2(arg0):
    ...


def exp(arg0):
    ...


def exp10(arg0):
    ...


def cosh(arg0):
    ...


def sinh(arg0):
    ...


def tanh(arg0):
    ...


def atan2(arg0, arg1):
    ...


def atan(arg0):
    ...


def asin(arg0):
    ...


def acos(arg0):
    ...


def log(arg0):
    ...


def log10(arg0):
    ...


def log1p(arg0):
    ...


def acosh(arg0):
    ...


def asinh(arg0):
    ...


def atanh(arg0):
    ...


def expm1(arg0):
    ...


def hypot(arg0, arg1):
    ...


def rhypot(arg0, arg1):
    ...


def norm3d(arg0, arg1, arg2):
    ...


def rnorm3d(arg0, arg1, arg2):
    ...


def norm4d(arg0, arg1, arg2, arg3):
    ...


def rnorm4d(arg0, arg1, arg2, arg3):
    ...


def cbrt(arg0):
    ...


def rcbrt(arg0):
    ...


def j0(arg0):
    ...


def j1(arg0):
    ...


def y0(arg0):
    ...


def y1(arg0):
    ...


def yn(arg0, arg1):
    ...


def jn(arg0, arg1):
    ...


def cyl_bessel_i0(arg0):
    ...


def cyl_bessel_i1(arg0):
    ...


def erf(arg0):
    ...


def erfinv(arg0):
    ...


def erfc(arg0):
    ...


def erfcx(arg0):
    ...


def erfcinv(arg0):
    ...


def normcdfinv(arg0):
    ...


def normcdf(arg0):
    ...


def lgamma(arg0):
    ...


def ldexp(arg0, arg1):
    ...


def scalbn(arg0, arg1):
    ...


def fmod(arg0, arg1):
    ...


def remainder(arg0, arg1):
    ...


def fma(arg0, arg1, arg2):
    ...


def pow(arg0, arg1):
    ...


def tgamma(arg0):
    ...


def round(arg0):
    ...


def llround(arg0):
    ...


def fdim(arg0, arg1):
    ...


def ilogb(arg0):
    ...


def logb(arg0):
    ...


def isfinited(arg0):
    ...
