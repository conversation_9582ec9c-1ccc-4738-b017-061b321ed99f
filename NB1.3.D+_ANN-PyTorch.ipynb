{"cells": [{"cell_type": "markdown", "id": "415ffb27", "metadata": {}, "source": ["<!-- CELL_ID: 15 -->\n", "## 📚 Part D: Loading and Preparing MNIST Data"]}, {"cell_type": "markdown", "id": "9e5d5818", "metadata": {}, "source": ["<!-- CELL_ID: 1 -->\n", "### Theory: Train/Validation/Test Split\n", "\n", "• **Why Three Sets?**\n", "  - **Training Set**: Update model weights\n", "  - **Validation Set**: Select best model/hyperparameters\n", "  - **Test Set**: Final unbiased performance estimate\n", "\n", "• **Common Splits:**\n", "  - Training: 60-80%\n", "  - Validation: 10-20%\n", "  - Test: 10-20%\n", "\n", "• **Cardinal Rule:**\n", "  - NEVER use test set during development\n", "  - It's a \"single-use\" resource\n", "  - Touching it multiple times → overestimation\n", "\n", "• **MNIST Convention:**\n", "  - Original: 60k train, 10k test\n", "  - We'll split: 50k train, 10k val, 10k test"]}, {"cell_type": "markdown", "id": "0253018a", "metadata": {}, "source": ["<!-- CELL_ID: 2 -->\n", "### 🎯 Task 4: Load MNIST with Proper Splits\n", "\n", "**Objective:** Load MNIST dataset and create train/val/test data loaders.\n", "\n", "**Specification for AI Assistant:**\n", "- Load MNIST using `torchvision.datasets.MNIST`\n", "- Apply transforms: `ToTensor()` and `Normalize((0.1307,), (0.3081,))`\n", "- Split original training set (60k) into train (50k) and validation (10k)\n", "- Use `random_split` with lengths [50000, 10000]\n", "- Create three DataLoaders with `batch_size=128`\n", "- Set `shuffle=True` for train, `shuffle=False` for val/test\n", "- Use settings from `get_data_loader_settings(device)` for pin_memory and num_workers\n", "- Store as `train_loader`, `val_loader`, `test_loader`\n", "- Print dataset sizes to confirm splits\n", "\n", "**Expected Output:**\n", "```\n", "Train set: 50,000 samples\n", "Validation set: 10,000 samples  \n", "Test set: 10,000 samples\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "d5618a33", "metadata": {}, "outputs": [], "source": ["# CELL_ID: 3\n", "# TODO: Implement MNIST data loading with proper splits\n", "# Use get_data_loader_settings(device) for appropriate loader settings\n", "\n", "# YOUR CODE HERE"]}, {"cell_type": "markdown", "id": "6cddab8c", "metadata": {}, "source": ["<!-- CELL_ID: 4 -->\n", "<details>\n", "<summary>💡 Need Hints? (click to expand)</summary>\n", "\n", "**Data Loading Steps:**\n", "1. Define transform with Compose([ToTensor(), Normalize(...)])\n", "2. Load full training set with `train=True`\n", "3. Load test set with `train=False`\n", "4. Use `random_split(dataset, [50000, 10000])` to split training\n", "5. <PERSON><PERSON> DataLoader for each split\n", "6. Use `**get_data_loader_settings(device)` to unpack settings\n", "\n", "**Common Pitfall:**\n", "- Don't forget to use the same transform for all sets\n", "- But only shuffle the training loader!\n", "\n", "**AI Prompt if Stuck:**\n", "\"Show me how to load MNIST in PyTorch with a train/validation/test split. \n", "Use random_split to divide the original training set. The DataLoader should\n", "use appropriate settings for the device type.\"\n", "</details>"]}, {"cell_type": "code", "execution_count": null, "id": "d385e665", "metadata": {}, "outputs": [], "source": ["# CELL_ID: 5\n", "# Test your data loading\n", "verify_data_loading(train_loader, val_loader, test_loader)  # type: ignore"]}, {"cell_type": "code", "execution_count": null, "id": "3a96f116", "metadata": {"lines_to_next_cell": 1}, "outputs": [], "source": ["# CELL_ID: 6\n", "# SOLUTION (uncomment to use):\n", "\"\"\"\n", "# Define transforms\n", "transform = transforms.Compose([\n", "    transforms.To<PERSON><PERSON><PERSON>(),\n", "    transforms.Normalize((0.1307,), (0.3081,))  # MNIST mean and std\n", "])\n", "\n", "# Load full datasets\n", "full_train_dataset = torchvision.datasets.MNIST(\n", "    root='./data', train=True, download=True, transform=transform\n", ")\n", "test_dataset = torchvision.datasets.MNIST(\n", "    root='./data', train=False, download=True, transform=transform\n", ")\n", "\n", "# Split training into train/val\n", "train_dataset, val_dataset = random_split(\n", "    full_train_dataset, [50000, 10000]\n", ")\n", "\n", "# Get appropriate settings for this device\n", "loader_settings = get_data_loader_settings(device)\n", "\n", "# Create data loaders\n", "train_loader = DataLoader(\n", "    train_dataset, batch_size=128, shuffle=True, **loader_settings\n", ")\n", "val_loader = DataLoader(\n", "    val_dataset, batch_size=128, shuffle=False, **loader_settings\n", ")\n", "test_loader = DataLoader(\n", "    test_dataset, batch_size=128, shuffle=False, **loader_settings\n", ")\n", "\n", "print(f\"Train set: {len(train_dataset):,} samples\")\n", "print(f\"Validation set: {len(val_dataset):,} samples\")\n", "print(f\"Test set: {len(test_dataset):,} samples\")\n", "\"\"\""]}, {"cell_type": "markdown", "id": "868c5971", "metadata": {}, "source": ["<!-- CELL_ID: 7 -->\n", "## 📚 Part E: Training the Neural Network"]}, {"cell_type": "markdown", "id": "11b49c4e", "metadata": {}, "source": ["<!-- CELL_ID: 8 -->\n", "### Theory: Training Loop Components\n", "\n", "• **Loss Function:**\n", "  - CrossEntropyLoss for classification\n", "  - Combines LogSoftmax + NLLLoss\n", "  - Expects raw logits (no softmax needed)\n", "\n", "• **Optimizer:**\n", "  - SGD: Simple, requires tuning\n", "  - Adam: Adaptive learning rates, robust\n", "  - Learning rate: Start with 1e-3\n", "\n", "• **Training Loop:**\n", "  1. Forward pass: predictions = model(inputs)\n", "  2. Compute loss: loss = criterion(predictions, targets)\n", "  3. Backward pass: loss.backward()\n", "  4. Update weights: optimizer.step()\n", "  5. Clear gradients: optimizer.zero_grad()\n", "\n", "• **Validation Loop:**\n", "  - No gradient computation (torch.no_grad())\n", "  - Model in eval mode (model.eval())\n", "  - Track best model based on val loss"]}, {"cell_type": "markdown", "id": "bdaca7f7", "metadata": {}, "source": ["<!-- CELL_ID: 9 -->\n", "### 🎯 Task 5: Implement Training Loop\n", "\n", "**Objective:** Train the model with proper validation monitoring.\n", "\n", "**Specification for AI Assistant:**\n", "- Create function `train_model(model, train_loader, val_loader, num_epochs=10, learning_rate=0.001)`\n", "- Move model to device (GPU if available) using global `device` variable\n", "- Use `nn.CrossEntropyLoss()` and `optim.<PERSON>()`\n", "- For each epoch:\n", "  - Train: Set model.train(), iterate batches, compute loss, backprop\n", "  - Validate: Set model.eval(), use torch.no_grad(), compute val loss/accuracy\n", "  - Print: \"Epoch X/Y | Train Loss: X.XXX | Val Loss: X.XXX | Val Acc: XX.X%\"\n", "- Track and store in dict: train_losses, val_losses, val_accuracies (lists)\n", "- Save best model state dict when val_loss improves\n", "- Return tuple: (history_dict, best_model_state_dict)\n", "\n", "**Expected Output Format:**\n", "```\n", "Epoch 1/10 | Train Loss: 0.543 | Val Loss: 0.221 | Val Acc: 92.1%\n", "Epoch 2/10 | Train Loss: 0.201 | Val Loss: 0.154 | Val Acc: 95.3%\n", "...\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "36ce107c", "metadata": {}, "outputs": [], "source": ["# CELL_ID: 10\n", "# TODO: Implement training loop function\n", "\n", "def train_model(model, train_loader, val_loader, num_epochs=10, learning_rate=0.001):\n", "    \"\"\"Train model with validation monitoring\"\"\"\n", "    # YOUR CODE HERE\n", "    pass\n", "\n", "# Create model and train\n", "model = TorchPerceptron([784, 128, 64, 10])\n", "history, best_model_state = train_model(model, train_loader, val_loader)  # type: ignore"]}, {"cell_type": "markdown", "id": "603a040c", "metadata": {}, "source": ["<!-- CELL_ID: 11 -->\n", "<details>\n", "<summary>💡 Need Hints? (click to expand)</summary>\n", "\n", "**Training Loop Structure:**\n", "```python\n", "for epoch in range(num_epochs):\n", "    # Training phase\n", "    model.train()\n", "    for batch_idx, (data, target) in enumerate(train_loader):\n", "        # Move to device\n", "        # Flatten images: data = data.view(data.size(0), -1)\n", "        # Forward, loss, backward, step\n", "    \n", "    # Validation phase  \n", "    model.eval()\n", "    with torch.no_grad():\n", "        # Compute validation metrics\n", "```\n", "\n", "**Computing Accuracy:**\n", "- Get predictions: `pred = output.argmax(dim=1)`\n", "- Count correct: `correct = (pred == target).sum().item()`\n", "- Accuracy = 100 * correct / total\n", "\n", "**AI Prompt if Stuck:**\n", "\"Show me a PyTorch training loop for MNIST classification. Include validation \n", "after each epoch and track the best model based on validation loss.\"\n", "</details>"]}, {"cell_type": "code", "execution_count": null, "id": "d6d15c9d", "metadata": {}, "outputs": [], "source": ["# CELL_ID: 12\n", "# Test your training implementation\n", "verify_training(history, model)"]}, {"cell_type": "code", "execution_count": null, "id": "f7b260f2", "metadata": {"lines_to_next_cell": 1}, "outputs": [], "source": ["# CELL_ID: 13\n", "# SOLUTION (uncomment to use):\n", "\"\"\"\n", "def train_model(model, train_loader, val_loader, num_epochs=10, learning_rate=0.001):\n", "    model = model.to(device)\n", "    criterion = nn.CrossEntropyLoss()\n", "    optimizer = optim.Adam(model.parameters(), lr=learning_rate)\n", "    \n", "    train_losses = []\n", "    val_losses = []\n", "    val_accuracies = []\n", "    best_val_loss = float('inf')\n", "    best_model_state = None\n", "    \n", "    for epoch in range(num_epochs):\n", "        # Training phase\n", "        model.train()\n", "        running_loss = 0.0\n", "        \n", "        for batch_idx, (data, target) in enumerate(train_loader):\n", "            data = data.view(data.size(0), -1).to(device)\n", "            target = target.to(device)\n", "            \n", "            optimizer.zero_grad()\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "            loss.backward()\n", "            optimizer.step()\n", "            \n", "            running_loss += loss.item()\n", "        \n", "        avg_train_loss = running_loss / len(train_loader)\n", "        train_losses.append(avg_train_loss)\n", "        \n", "        # Validation phase\n", "        model.eval()\n", "        val_loss = 0\n", "        correct = 0\n", "        total = 0\n", "        \n", "        with torch.no_grad():\n", "            for data, target in val_loader:\n", "                data = data.view(data.size(0), -1).to(device)\n", "                target = target.to(device)\n", "                \n", "                output = model(data)\n", "                val_loss += criterion(output, target).item()\n", "                \n", "                pred = output.argmax(dim=1)\n", "                correct += (pred == target).sum().item()\n", "                total += target.size(0)\n", "        \n", "        avg_val_loss = val_loss / len(val_loader)\n", "        val_accuracy = 100 * correct / total\n", "        \n", "        val_losses.append(avg_val_loss)\n", "        val_accuracies.append(val_accuracy)\n", "        \n", "        # Save best model\n", "        if avg_val_loss < best_val_loss:\n", "            best_val_loss = avg_val_loss\n", "            best_model_state = model.state_dict().copy()\n", "        \n", "        print(f\"Epoch {epoch+1}/{num_epochs} | \"\n", "              f\"Train Loss: {avg_train_loss:.3f} | \"\n", "              f\"Val Loss: {avg_val_loss:.3f} | \"\n", "              f\"Val Acc: {val_accuracy:.1f}%\")\n", "    \n", "    history = {\n", "        'train_losses': train_losses,\n", "        'val_losses': val_losses,\n", "        'val_accuracies': val_accuracies\n", "    }\n", "    \n", "    return history, best_model_state\n", "\"\"\""]}, {"cell_type": "markdown", "id": "8fb8a74b", "metadata": {}, "source": ["<!-- CELL_ID: 14 -->\n", "## 📚 Part F: Final Evaluation"]}, {"cell_type": "markdown", "id": "6b9c9e1d", "metadata": {}, "source": ["<!-- CELL_ID: 15 -->\n", "### Theory: Test Set Evaluation\n", "\n", "• **Why a Separate Test Set?**\n", "  - Validation set used for model selection\n", "  - Risk of overfitting to validation set\n", "  - Test set provides unbiased estimate\n", "\n", "• **Evaluation Metrics:**\n", "  - **Accuracy**: Overall correct predictions\n", "  - **Confusion Matrix**: Per-class performance\n", "  - **Precision/Recall**: For imbalanced data\n", "  - **F1-Score**: Harmonic mean of P&R\n", "\n", "• **Visualization:**\n", "  - Show correctly classified examples\n", "  - Analyze failure cases\n", "  - Look for patterns in errors"]}, {"cell_type": "markdown", "id": "84921600", "metadata": {}, "source": ["<!-- CELL_ID: 16 -->\n", "### 🎯 Task 6: Final Model Evaluation\n", "\n", "**Objective:** Evaluate the best model on the test set.\n", "\n", "**Specification for AI Assistant:**\n", "- Create function `evaluate_model(model, test_loader, model_state_dict)`\n", "- Load the best model weights using `model.load_state_dict()`\n", "- Move model to device and set to eval mode\n", "- Compute test accuracy and loss\n", "- Generate confusion matrix using sklearn.metrics.confusion_matrix\n", "- Plot confusion matrix as heatmap with seaborn\n", "- Use `plot_sample_predictions(model, test_loader, device)` to show examples\n", "- Return test_accuracy, test_loss, confusion_matrix array\n", "\n", "**Expected Output:**\n", "- Print: \"Test Accuracy: XX.X% | Test Loss: X.XXX\"\n", "- Confusion matrix heatmap\n", "- Grid of sample predictions"]}, {"cell_type": "code", "execution_count": null, "id": "d20d4f15", "metadata": {}, "outputs": [], "source": ["# CELL_ID: 17\n", "# TODO: Implement model evaluation function\n", "\n", "def evaluate_model(model, test_loader, model_state_dict):\n", "    \"\"\"Evaluate model on test set with visualizations\"\"\"\n", "    # YOUR CODE HERE\n", "    pass\n", "\n", "# Evaluate the best model\n", "test_acc, test_loss, conf_matrix = evaluate_model(model, test_loader, best_model_state)  # type: ignore"]}, {"cell_type": "markdown", "id": "0043c9e0", "metadata": {}, "source": ["<!-- CELL_ID: 18 -->\n", "<details>\n", "<summary>💡 Need Hints? (click to expand)</summary>\n", "\n", "**Evaluation Steps:**\n", "1. Load best weights: `model.load_state_dict(model_state_dict)`\n", "2. Move to device: `model = model.to(device)`\n", "3. Set eval mode: `model.eval()`\n", "4. Iterate test set with `torch.no_grad()`\n", "5. Collect all predictions and true labels\n", "6. Use `confusion_matrix(y_true, y_pred)`\n", "\n", "**Visualization Tips:**\n", "- Use `plt.figure(figsize=(8, 6))` for confusion matrix\n", "- `sns.heatmap()` with `annot=True, fmt='d'`\n", "- Call `plot_sample_predictions()` from framework\n", "\n", "**AI Prompt if Stuck:**\n", "\"Show me how to evaluate a PyTorch model on a test set, including creating \n", "a confusion matrix and visualizing predictions. The model needs to be loaded\n", "with saved weights first.\"\n", "</details>"]}, {"cell_type": "code", "execution_count": null, "id": "a336784a", "metadata": {}, "outputs": [], "source": ["# CELL_ID: 19\n", "# Test your evaluation implementation\n", "verify_evaluation(test_acc, test_loss, conf_matrix)"]}, {"cell_type": "code", "execution_count": null, "id": "a7beac01", "metadata": {}, "outputs": [], "source": ["# CELL_ID: 20\n", "# SOLUTION (uncomment to use):\n", "\"\"\"\n", "def evaluate_model(model, test_loader, model_state_dict):\n", "    # Load best model\n", "    model.load_state_dict(model_state_dict)\n", "    model = model.to(device)\n", "    model.eval()\n", "    \n", "    criterion = nn.CrossEntropyLoss()\n", "    test_loss = 0\n", "    correct = 0\n", "    all_preds = []\n", "    all_targets = []\n", "    \n", "    # Collect predictions\n", "    with torch.no_grad():\n", "        for data, target in test_loader:\n", "            data = data.view(data.size(0), -1).to(device)\n", "            target = target.to(device)\n", "            \n", "            output = model(data)\n", "            test_loss += criterion(output, target).item()\n", "            \n", "            pred = output.argmax(dim=1)\n", "            correct += (pred == target).sum().item()\n", "            \n", "            all_preds.extend(pred.cpu().numpy())\n", "            all_targets.extend(target.cpu().numpy())\n", "    \n", "    # Calculate metrics\n", "    test_loss /= len(test_loader)\n", "    test_accuracy = 100 * correct / len(test_loader.dataset)\n", "    \n", "    print(f\"Test Accuracy: {test_accuracy:.1f}% | Test Loss: {test_loss:.3f}\")\n", "    \n", "    # Confusion matrix\n", "    conf_matrix = confusion_matrix(all_targets, all_preds)\n", "    \n", "    plt.figure(figsize=(8, 6))\n", "    sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues', \n", "                xticklabels=range(10), yticklabels=range(10))\n", "    plt.xlabel('Predicted Label')\n", "    plt.ylabel('True Label')\n", "    plt.title('Confusion Matrix on Test Set')\n", "    plt.show()\n", "    \n", "    # Show sample predictions\n", "    plot_sample_predictions(model, test_loader, device)\n", "    \n", "    return test_accuracy, test_loss, conf_matrix\n", "\"\"\""]}, {"cell_type": "markdown", "id": "8b7e196d", "metadata": {}, "source": ["<!-- CELL_ID: 21 -->\n", "## 🎯 Summary and Next Steps"]}, {"cell_type": "markdown", "id": "52a424ca", "metadata": {}, "source": ["<!-- CELL_ID: 22 -->\n", "### What You've Accomplished:\n", "\n", "✅ **Built a neuron from scratch** - Understanding the fundamental computation  \n", "✅ **Vectorized to layers** - Leveraging matrix operations for efficiency  \n", "✅ **Transitioned to PyTorch** - Automatic differentiation and GPU support  \n", "✅ **Proper data splits** - Train/val/test methodology  \n", "✅ **Training with validation** - Monitoring and preventing overfitting  \n", "✅ **Comprehensive evaluation** - Metrics and visualizations  \n", "\n", "### Key Takeaways:\n", "\n", "• **Neural networks are just matrix multiplications + non-linearities**  \n", "• **Proper validation is crucial for generalization**  \n", "• **PyTorch handles the hard parts (gradients) for you**  \n", "• **Always evaluate on unseen test data**  \n", "\n", "### Next in Notebook 2 - CNNs:\n", "\n", "• **Convolutional layers** for spatial pattern detection  \n", "• **Pooling** for translation invariance  \n", "• **Much better accuracy** on image tasks  \n", "• **Fewer parameters** through weight sharing  \n", "\n", "### 🚀 Optional Challenges:\n", "\n", "1. **Experiment with architecture**: Try [784, 256, 128, 64, 10]\n", "2. **Different optimizers**: Compare SGD vs Adam vs RMSprop\n", "3. **Learning rate scheduling**: Decay LR during training\n", "4. **Regularization**: Add dropout layers to prevent overfitting\n", "5. **Data augmentation**: Random rotations and shifts\n", "\n", "### 📚 Further Reading:\n", "\n", "- [Deep Learning Book - <PERSON>](http://www.deeplearningbook.org/)\n", "- [PyTorch Tutorials](https://pytorch.org/tutorials/)\n", "- [Neural Networks and Deep Learning - <PERSON>](http://neuralnetworksanddeeplearning.com/)\n", "- [Fast.ai Practical Deep Learning](https://course.fast.ai/)"]}, {"cell_type": "code", "execution_count": null, "id": "6b7e9182", "metadata": {}, "outputs": [], "source": ["# CELL_ID: 23\n", "# Save your notebook results\n", "results = {\n", "    \"timestamp\": datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "    \"final_test_accuracy\": test_acc if 'test_acc' in locals() else None,\n", "    \"device_used\": str(device),\n", "    \"progress\": _progress  # type: ignore\n", "}\n", "\n", "print(\"\\n🎉 Notebook 1 Complete!\")\n", "print(f\"Final test accuracy: {test_acc:.1f}%\" if 'test_acc' in locals() else \"Complete all tasks to see final results\")"]}], "metadata": {"jupytext": {"cell_metadata_filter": "-all", "main_language": "python", "notebook_metadata_filter": "-all"}, "kernelspec": {"display_name": "pytorch_course_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}